<template>
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-gray-900">通知中心</h1>

        <div class="flex items-center space-x-3">
          <UButton
            v-if="notificationsStore.hasUnread"
            variant="outline"
            size="sm"
            @click="notificationsStore.markAllAsRead"
          >
            全部已读
          </UButton>

          <UDropdown :items="moreActions">
            <UButton variant="ghost" size="sm">
              <Icon name="heroicons:ellipsis-horizontal" class="w-4 h-4" />
            </UButton>
          </UDropdown>
        </div>
      </div>

      <!-- 通知筛选 -->
      <div class="flex items-center space-x-4 mb-6">
        <UButton
          v-for="filter in notificationFilters"
          :key="filter.key"
          :variant="activeFilter === filter.key ? 'solid' : 'ghost'"
          size="sm"
          @click="setFilter(filter.key)"
        >
          {{ filter.label }}
          <UBadge
            v-if="filter.key === 'unread' && notificationsStore.unreadCount > 0"
            color="red"
            variant="solid"
            size="xs"
            class="ml-1"
          >
            {{ notificationsStore.unreadCount }}
          </UBadge>
        </UButton>
      </div>

      <!-- 通知列表 -->
      <div class="space-y-2">
        <!-- 加载状态 -->
        <div v-if="notificationsStore.isLoading && filteredNotifications.length === 0" class="space-y-2">
          <div v-for="i in 5" :key="i" class="animate-pulse">
            <div class="bg-gray-100 h-16 rounded-lg"></div>
          </div>
        </div>

        <!-- 通知项 -->
        <div
          v-for="notification in filteredNotifications"
          :key="notification.id"
          @click="handleNotificationClick(notification)"
          class="flex items-start space-x-4 p-4 rounded-lg border cursor-pointer transition-colors"
          :class="{
            'bg-blue-50 border-blue-200': !notification.isRead,
            'bg-white border-gray-200 hover:bg-gray-50': notification.isRead
          }"
        >
          <!-- 通知图标 -->
          <div class="flex-shrink-0">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center"
              :class="`bg-${notificationsStore.getNotificationColor(notification.type)}-100`"
            >
              <Icon
                :name="notificationsStore.getNotificationIcon(notification.type)"
                class="w-5 h-5"
                :class="`text-${notificationsStore.getNotificationColor(notification.type)}-600`"
              />
            </div>
          </div>

          <!-- 通知内容 -->
          <div class="flex-1 min-w-0">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="font-medium text-gray-900 mb-1">{{ notification.title }}</h3>
                <p class="text-sm text-gray-600 line-clamp-2">{{ notification.content }}</p>
                <div class="flex items-center space-x-2 mt-2">
                  <span class="text-xs text-gray-500">
                    {{ notificationsStore.formatNotificationTime(notification.createdAt) }}
                  </span>
                  <UBadge
                    :color="notificationsStore.getNotificationColor(notification.type)"
                    variant="subtle"
                    size="xs"
                  >
                    {{ getNotificationTypeText(notification.type) }}
                  </UBadge>
                </div>
              </div>

              <!-- 未读标识 -->
              <div v-if="!notification.isRead" class="flex-shrink-0 ml-2">
                <div class="w-2 h-2 bg-blue-600 rounded-full"></div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex-shrink-0">
            <UDropdown :items="getNotificationActions(notification)">
              <UButton variant="ghost" size="sm" @click.stop>
                <Icon name="heroicons:ellipsis-vertical" class="w-4 h-4" />
              </UButton>
            </UDropdown>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!notificationsStore.isLoading && filteredNotifications.length === 0" class="text-center py-12">
          <Icon name="heroicons:bell-slash" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-semibold text-gray-600 mb-2">暂无通知</h3>
          <p class="text-gray-500">{{ getEmptyStateText() }}</p>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="notificationsStore.pagination.totalPages > 1" class="flex justify-center mt-8">
        <UPagination
          v-model="notificationsStore.pagination.page"
          :page-count="notificationsStore.pagination.pageSize"
          :total="notificationsStore.pagination.total"
          @update:model-value="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Notification } from '~/types'

// 页面SEO
useHead({
  title: '通知中心 - 社交购物网站'
})

// 状态管理
const notificationsStore = useNotificationsStore()

// 响应式数据
const activeFilter = ref('all')

// 通知筛选选项
const notificationFilters = [
  { key: 'all', label: '全部' },
  { key: 'unread', label: '未读' },
  { key: 'LIKE', label: '点赞' },
  { key: 'COMMENT', label: '评论' },
  { key: 'FOLLOW', label: '关注' },
  { key: 'ORDER', label: '订单' },
  { key: 'SYSTEM', label: '系统' }
]

// 更多操作菜单
const moreActions = [
  [
    {
      label: '清空所有通知',
      icon: 'heroicons:trash',
      click: () => handleClearAll()
    }
  ]
]

// 计算属性
const filteredNotifications = computed(() => {
  if (activeFilter.value === 'all') {
    return notificationsStore.notifications
  } else if (activeFilter.value === 'unread') {
    return notificationsStore.unreadNotifications
  } else {
    return notificationsStore.notifications.filter(n => n.type === activeFilter.value)
  }
})

// 设置筛选条件
const setFilter = (filterKey: string) => {
  activeFilter.value = filterKey
  notificationsStore.pagination.page = 1
  fetchNotifications()
}

// 获取通知列表
const fetchNotifications = async () => {
  const params: Record<string, any> = {}

  if (activeFilter.value === 'unread') {
    params.isRead = false
  } else if (activeFilter.value !== 'all') {
    params.type = activeFilter.value
  }

  await notificationsStore.fetchNotifications(params)
}

// 处理分页
const handlePageChange = (page: number) => {
  notificationsStore.pagination.page = page
  fetchNotifications()
}

// 处理通知点击
const handleNotificationClick = async (notification: Notification) => {
  await notificationsStore.handleNotificationClick(notification)
}

// 获取通知操作菜单
const getNotificationActions = (notification: Notification) => {
  const actions = []

  if (!notification.isRead) {
    actions.push({
      label: '标记已读',
      icon: 'heroicons:check',
      click: () => notificationsStore.markAsRead(notification.id)
    })
  }

  actions.push({
    label: '删除',
    icon: 'heroicons:trash',
    click: () => notificationsStore.deleteNotification(notification.id)
  })

  return [actions]
}

// 获取通知类型文本
const getNotificationTypeText = (type: string) => {
  const texts = {
    LIKE: '点赞',
    COMMENT: '评论',
    FOLLOW: '关注',
    ORDER: '订单',
    PAYMENT: '支付',
    SYSTEM: '系统',
    PROMOTION: '促销'
  }
  return texts[type as keyof typeof texts] || type
}

// 获取空状态文本
const getEmptyStateText = () => {
  if (activeFilter.value === 'unread') {
    return '所有通知都已读完了'
  } else if (activeFilter.value === 'all') {
    return '还没有收到任何通知'
  } else {
    return `暂无${getNotificationTypeText(activeFilter.value)}通知`
  }
}

// 处理清空所有通知
const handleClearAll = async () => {
  if (confirm('确定要清空所有通知吗？此操作不可恢复。')) {
    await notificationsStore.clearAllNotifications()
    await fetchNotifications()
  }
}

// 页面加载时获取通知列表
onMounted(() => {
  fetchNotifications()

  // 请求浏览器通知权限
  notificationsStore.requestNotificationPermission()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
