/**
 * 清除用户搜索历史
 * DELETE /api/search/history
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  // 只允许DELETE请求
  assertMethod(event, 'DELETE')

  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  const query = getQuery(event)
  const historyId = query.id ? parseInt(query.id as string) : null

  try {
    if (historyId) {
      // 删除指定的搜索历史记录
      // 在实际项目中，这里应该从数据库删除指定记录
      console.log(`删除搜索历史记录: 用户ID=${userId}, 历史ID=${historyId}`)

      return createSuccessResponse(null, '搜索历史记录已删除')
    } else {
      // 清除所有搜索历史
      // 在实际项目中，这里应该从数据库删除用户的所有搜索历史
      console.log(`清除所有搜索历史: 用户ID=${userId}`)

      return createSuccessResponse(null, '搜索历史已清空')
    }
  } catch (error) {
    console.error('清除搜索历史失败:', error)
    throw new InternalServerError('清除搜索历史失败')
  }
})
