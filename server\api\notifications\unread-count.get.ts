/**
 * 获取未读通知数量API
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  try {
    // 检查用户认证
    const currentUser = event.context.user
    if (!currentUser) {
      throw createError({
        statusCode: 401,
        statusMessage: '请先登录'
      })
    }

    // 模拟未读通知数量
    // 在实际应用中，这里应该查询数据库
    const unreadCount = Math.floor(Math.random() * 10) + 1

    return unreadCount
  } catch (error: any) {
    console.error('获取未读通知数量失败:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || '获取未读通知数量失败'
    })
  }
})
