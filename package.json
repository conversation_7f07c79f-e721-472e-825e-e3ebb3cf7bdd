{"name": "social-shop", "version": "1.0.0", "description": "社交购物网站 - 基于Nuxt.js全栈开发", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare && prisma generate", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "nuxt typecheck", "prepare": "simple-git-hooks", "setup": "bash scripts/setup.sh", "setup:win": "scripts/setup.bat", "setup:skip": "bash scripts/setup.sh --skip-config", "setup:skip:win": "scripts/setup.bat --skip-config", "setup:silent": "bash scripts/setup.sh --silent", "setup:silent:win": "scripts/setup.bat --silent", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:seed": "prisma db seed", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "db:init": "bash scripts/init-db.sh", "db:init:win": "scripts/init-db.bat", "db:test": "bash scripts/test-db-connection.sh", "db:test:win": "scripts/test-db-connection.bat", "uploads:init": "bash scripts/init-uploads.sh", "uploads:init:win": "scripts/init-uploads.bat", "dev:setup": "bash scripts/dev-setup.sh", "dev:setup:win": "powershell -ExecutionPolicy Bypass -File scripts/dev-setup.ps1", "dev:services": "docker-compose -f docker-compose.dev.yml up -d", "dev:services:stop": "docker-compose -f docker-compose.dev.yml down", "dev:services:logs": "docker-compose -f docker-compose.dev.yml logs -f", "dev:clean": "rm -rf .nuxt .output node_modules/.cache"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "simple-git-hooks": {"pre-commit": "npx lint-staged", "commit-msg": "npx commitlint --edit $1"}, "lint-staged": {"*.{js,ts,vue}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@nuxt/devtools": "latest", "@nuxt/eslint-config": "^0.2.0", "@nuxt/ui": "^2.11.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.3.0", "@types/pg": "^8.10.9", "eslint": "^8.56.0", "lint-staged": "^15.2.0", "nuxt": "^3.8.0", "prettier": "^3.1.1", "simple-git-hooks": "^2.9.0", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "dependencies": {"@nuxtjs/google-fonts": "^3.0.2", "@pinia/nuxt": "^0.5.1", "@prisma/client": "^5.7.1", "@vueuse/nuxt": "^10.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "pinia": "^2.1.7", "prisma": "^5.7.1", "redis": "^4.6.11", "vue-tsc": "^3.0.6", "zod": "^3.22.4"}, "engines": {"node": ">=18.0.0"}, "packageManager": "yarn@4.9.4"}