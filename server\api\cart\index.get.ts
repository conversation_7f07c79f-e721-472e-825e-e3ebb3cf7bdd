/**
 * 获取购物车列表
 * GET /api/cart
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  // 查询购物车商品
  const cartItems = await prisma.cartItem.findMany({
    where: { userId },
    orderBy: { createdAt: 'desc' },
    include: {
      product: {
        select: {
          id: true,
          name: true,
          price: true,
          originalPrice: true,
          images: true,
          stock: true,
          status: true,
          merchant: {
            select: {
              id: true,
              username: true,
              nickname: true
            }
          }
        }
      }
    }
  })

  // 过滤掉已下架或删除的商品
  const validCartItems = cartItems.filter(item => item.product.status === 'ACTIVE')

  // 计算购物车统计信息
  const totalItems = validCartItems.reduce((sum, item) => sum + item.quantity, 0)
  const selectedItems = validCartItems.filter(item => item.selected)
  const totalAmount = selectedItems.reduce((sum, item) => sum + item.product.price.toNumber() * item.quantity, 0)
  const totalOriginalAmount = selectedItems.reduce(
    (sum, item) => sum + (item.product.originalPrice?.toNumber() || item.product.price.toNumber()) * item.quantity,
    0
  )
  const discountAmount = totalOriginalAmount - totalAmount

  // 格式化购物车数据
  const formattedCartItems = validCartItems.map(item => ({
    id: item.id,
    quantity: item.quantity,
    selected: item.selected,
    createdAt: item.createdAt,
    updatedAt: item.updatedAt,
    product: {
      ...item.product,
      price: item.product.price.toNumber(),
      originalPrice: item.product.originalPrice?.toNumber() || null
    }
  }))

  const cartSummary = {
    items: formattedCartItems,
    summary: {
      totalItems,
      selectedCount: selectedItems.length,
      totalAmount,
      totalOriginalAmount,
      discountAmount
    }
  }

  return createSuccessResponse(cartSummary, '获取购物车成功')
})
