/**
 * 领取优惠券
 * POST /api/coupons/:id/claim
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  // 只允许POST请求
  assertMethod(event, 'POST')

  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  // 获取优惠券ID
  const couponId = parseInt(getRouterParam(event, 'id') || '0')
  if (!couponId || isNaN(couponId)) {
    throw new ValidationError('优惠券ID格式不正确')
  }

  try {
    // 开始事务
    const result = await prisma.$transaction(async tx => {
      // 检查优惠券是否存在且可用
      const coupon = await tx.coupon.findUnique({
        where: { id: couponId }
      })

      if (!coupon) {
        throw new NotFoundError('优惠券不存在')
      }

      if (coupon.status !== 'ACTIVE') {
        throw new BusinessError('优惠券已失效')
      }

      const now = new Date()
      if (now < coupon.startTime) {
        throw new BusinessError('优惠券尚未开始')
      }

      if (now > coupon.endTime) {
        throw new BusinessError('优惠券已过期')
      }

      // 检查使用次数限制
      if (coupon.usedCount >= coupon.usageLimit) {
        throw new BusinessError('优惠券已被领完')
      }

      // 检查用户是否已经领取过
      const existingUserCoupon = await tx.userCoupon.findFirst({
        where: {
          userId,
          couponId
        }
      })

      if (existingUserCoupon) {
        throw new BusinessError('您已经领取过该优惠券')
      }

      // 检查用户每日领取限制（可选）
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)

      const todayClaimedCount = await tx.userCoupon.count({
        where: {
          userId,
          createdAt: {
            gte: today,
            lt: tomorrow
          }
        }
      })

      const dailyLimit = 10 // 每日最多领取10张优惠券
      if (todayClaimedCount >= dailyLimit) {
        throw new BusinessError('今日领取优惠券已达上限')
      }

      // 创建用户优惠券记录
      const userCoupon = await tx.userCoupon.create({
        data: {
          userId,
          couponId,
          status: 'UNUSED',
          receivedAt: now,
          expiresAt: coupon.endTime
        }
      })

      // 更新优惠券使用次数
      await tx.coupon.update({
        where: { id: couponId },
        data: {
          usedCount: { increment: 1 }
        }
      })

      return {
        userCoupon,
        coupon
      }
    })

    // 格式化返回数据
    const formattedResult = {
      id: result.userCoupon.id,
      couponId: result.coupon.id,
      name: result.coupon.name,
      description: result.coupon.description,
      type: result.coupon.type,
      discountType: result.coupon.discountType,
      discountValue: result.coupon.discountValue.toNumber(),
      minOrderAmount: result.coupon.minOrderAmount?.toNumber() || 0,
      maxDiscountAmount: result.coupon.maxDiscountAmount?.toNumber() || null,
      status: result.userCoupon.status,
      receivedAt: result.userCoupon.receivedAt,
      expiresAt: result.userCoupon.expiresAt
    }

    return createSuccessResponse(formattedResult, '优惠券领取成功')
  } catch (error) {
    console.error('领取优惠券失败:', error)
    if (error instanceof BusinessError || error instanceof ValidationError || error instanceof NotFoundError) {
      throw error
    }
    throw new InternalServerError('领取优惠券失败')
  }
})
