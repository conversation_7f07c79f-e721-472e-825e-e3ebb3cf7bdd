import { z } from 'zod'
import { defineApiHandler } from '~/utils/api'

/**
 * 创建订单
 * POST /api/orders
 */

// 请求数据验证schema
const createOrderSchema = z.object({
  items: z
    .array(
      z.object({
        productId: z.number().int().positive(),
        quantity: z.number().int().min(1).max(999)
      })
    )
    .min(1, '订单商品不能为空'),
  shippingAddressId: z.number().int().positive('收货地址ID必须是正整数'),
  remark: z.string().max(500, '备注最多500个字符').optional()
})

export default defineApiHandler(async event => {
  // 只允许POST请求
  assertMethod(event, 'POST')

  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  // 获取请求体数据
  const body = await readBody(event)

  // 验证请求数据
  const { items, shippingAddressId, remark } = createOrderSchema.parse(body)

  // 验证收货地址
  const shippingAddress = await prisma.shippingAddress.findFirst({
    where: {
      id: shippingAddressId,
      userId
    }
  })

  if (!shippingAddress) {
    throw new NotFoundError('收货地址不存在')
  }

  // 验证商品信息并计算价格
  const productIds = items.map(item => item.productId)
  const products = await prisma.product.findMany({
    where: {
      id: { in: productIds },
      status: 'ACTIVE'
    }
  })

  if (products.length !== productIds.length) {
    throw new BusinessError('部分商品不存在或已下架')
  }

  // 检查库存并计算总价
  let totalAmount = 0
  const orderItems = []

  for (const item of items) {
    const product = products.find(p => p.id === item.productId)
    if (!product) {
      throw new BusinessError(`商品ID ${item.productId} 不存在`)
    }

    if (product.stock < item.quantity) {
      throw new BusinessError(`商品 ${product.name} 库存不足`)
    }

    const itemTotal = product.price.toNumber() * item.quantity
    totalAmount += itemTotal

    orderItems.push({
      productId: product.id,
      productName: product.name,
      productImage: product.images[0] || '',
      price: product.price,
      quantity: item.quantity,
      totalAmount: itemTotal
    })
  }

  // 计算运费（简单逻辑，实际应该根据地址和重量计算）
  const shippingAmount = totalAmount >= 99 ? 0 : 10

  // 计算优惠金额（这里简化处理）
  const discountAmount = 0

  // 最终支付金额
  const paymentAmount = totalAmount + shippingAmount - discountAmount

  // 生成订单号
  const orderNo = `ORD${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`

  // 使用事务创建订单
  const order = await prisma.$transaction(async tx => {
    // 创建订单
    const newOrder = await tx.order.create({
      data: {
        orderNo,
        userId,
        totalAmount,
        discountAmount,
        shippingAmount,
        paymentAmount,
        status: 'PENDING',
        paymentStatus: 'PENDING',
        shippingAddressId,
        remark,
        items: {
          create: orderItems
        }
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                images: true
              }
            }
          }
        },
        shippingAddress: true
      }
    })

    // 减少商品库存
    for (const item of items) {
      await tx.product.update({
        where: { id: item.productId },
        data: {
          stock: { decrement: item.quantity },
          sales: { increment: item.quantity }
        }
      })
    }

    // 清空购物车中的相关商品
    await tx.cartItem.deleteMany({
      where: {
        userId,
        productId: { in: productIds }
      }
    })

    return newOrder
  })

  // 格式化返回数据
  const formattedOrder = {
    ...order,
    totalAmount: order.totalAmount.toNumber(),
    discountAmount: order.discountAmount.toNumber(),
    shippingAmount: order.shippingAmount.toNumber(),
    paymentAmount: order.paymentAmount.toNumber(),
    items: order.items.map(item => ({
      ...item,
      price: item.price.toNumber(),
      totalAmount: item.totalAmount.toNumber()
    }))
  }

  return createSuccessResponse(formattedOrder, '订单创建成功')
})
