/**
 * 获取商品分类列表
 * GET /api/categories
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  const query = getQuery(event)

  // 是否只获取顶级分类
  const topLevelOnly = query.topLevel === 'true'

  // 构建查询条件
  const where: any = {
    status: 'ACTIVE'
  }

  if (topLevelOnly) {
    where.parentId = null
  }

  // 查询分类列表
  const categories = await prisma.category.findMany({
    where,
    orderBy: { sort: 'asc' },
    include: {
      children: topLevelOnly
        ? {
            where: { status: 'ACTIVE' },
            orderBy: { sort: 'asc' },
            include: {
              _count: {
                select: {
                  products: {
                    where: { status: 'ACTIVE' }
                  }
                }
              }
            }
          }
        : false,
      _count: {
        select: {
          products: {
            where: { status: 'ACTIVE' }
          }
        }
      }
    }
  })

  // 格式化分类数据
  const formattedCategories = categories.map(category => ({
    ...category,
    productsCount: category._count?.products || 0,
    children: category.children?.map(child => ({
      ...child,
      productsCount: child._count?.products || 0,
      _count: undefined
    })),
    _count: undefined
  }))

  return createSuccessResponse(formattedCategories, '获取分类列表成功')
})
