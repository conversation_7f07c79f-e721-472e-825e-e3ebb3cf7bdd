import type { ApiResponse, LoginForm, RegisterForm, User } from '~/types'

/**
 * 用户认证状态管理
 */
export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isLoading = ref(false)
  const isLoggedIn = computed(() => !!user.value && !!token.value)

  // 初始化认证状态
  const initAuth = () => {
    if (process.client) {
      const savedToken = localStorage.getItem('auth-token')
      const savedUser = localStorage.getItem('auth-user')

      if (savedToken && savedUser) {
        try {
          token.value = savedToken
          user.value = JSON.parse(savedUser)
        } catch (error) {
          console.error('解析用户信息失败:', error)
          clearAuth()
        }
      }
    }
  }

  // 设置认证信息
  const setAuth = (userData: User, authToken: string) => {
    user.value = userData
    token.value = authToken

    if (process.client) {
      localStorage.setItem('auth-token', authToken)
      localStorage.setItem('auth-user', JSON.stringify(userData))
    }
  }

  // 清除认证信息
  const clearAuth = () => {
    user.value = null
    token.value = null

    if (process.client) {
      localStorage.removeItem('auth-token')
      localStorage.removeItem('auth-user')
    }
  }

  // 用户登录
  const login = async (credentials: LoginForm): Promise<void> => {
    isLoading.value = true

    try {
      const response = await $fetch<
        ApiResponse<{
          user: User
          token: string
          expiresIn: string
        }>
      >('/api/auth/login', {
        method: 'POST',
        body: credentials
      })

      if (response.success && response.data) {
        setAuth(response.data.user, response.data.token)

        // 显示成功消息
        const toast = useToast()
        toast.add({
          title: '登录成功',
          description: `欢迎回来，${response.data.user.nickname || response.data.user.username}！`,
          color: 'green'
        })
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error: any) {
      const toast = useToast()
      toast.add({
        title: '登录失败',
        description: error.data?.message || error.message || '请检查用户名和密码',
        color: 'red'
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 用户注册
  const register = async (userData: RegisterForm): Promise<void> => {
    isLoading.value = true

    try {
      const response = await $fetch<
        ApiResponse<{
          user: User
          token: string
        }>
      >('/api/auth/register', {
        method: 'POST',
        body: userData
      })

      if (response.success && response.data) {
        setAuth(response.data.user, response.data.token)

        // 显示成功消息
        const toast = useToast()
        toast.add({
          title: '注册成功',
          description: `欢迎加入，${response.data.user.nickname || response.data.user.username}！`,
          color: 'green'
        })
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error: any) {
      const toast = useToast()
      toast.add({
        title: '注册失败',
        description: error.data?.message || error.message || '请检查注册信息',
        color: 'red'
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 用户登出
  const logout = async () => {
    try {
      // 调用登出API
      if (token.value) {
        await $fetch<ApiResponse<null>>('/api/auth/logout', {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token.value}`
          }
        })
      }

      clearAuth()

      const toast = useToast()
      toast.add({
        title: '已退出登录',
        description: '感谢您的使用，期待您的再次光临！',
        color: 'blue'
      })

      // 跳转到首页
      await navigateTo('/')
    } catch (error) {
      console.error('登出失败:', error)
      // 即使API调用失败，也要清除本地认证信息
      clearAuth()

      const toast = useToast()
      toast.add({
        title: '已退出登录',
        description: '感谢您的使用，期待您的再次光临！',
        color: 'blue'
      })

      // 跳转到首页
      await navigateTo('/')
    }
  }

  // 刷新用户信息
  const refreshUser = async (): Promise<void> => {
    if (!token.value) return

    try {
      const response = await $fetch<ApiResponse<User>>('/api/users/profile', {
        headers: {
          Authorization: `Bearer ${token.value}`
        }
      })

      if (response.success && response.data) {
        user.value = response.data

        if (process.client) {
          localStorage.setItem('auth-user', JSON.stringify(response.data))
        }
      }
    } catch (error: any) {
      console.error('刷新用户信息失败:', error)
      // 如果token无效，清除认证信息
      if (error.statusCode === 401) {
        clearAuth()
      }
    }
  }

  // 检查token有效性
  const validateToken = async (): Promise<boolean> => {
    if (!token.value) return false

    try {
      await refreshUser()
      return !!user.value
    } catch (error) {
      return false
    }
  }

  // 获取认证头
  const getAuthHeaders = () => {
    return token.value
      ? {
          Authorization: `Bearer ${token.value}`
        }
      : {}
  }

  // 检查权限
  const hasPermission = (requiredRole: string): boolean => {
    if (!user.value) return false

    const roleHierarchy = {
      ADMIN: 3,
      MERCHANT: 2,
      USER: 1
    }

    const userLevel = roleHierarchy[user.value.role as keyof typeof roleHierarchy] || 0
    const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0

    return userLevel >= requiredLevel
  }

  // 初始化时恢复认证状态
  if (process.client) {
    initAuth()
  }

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    isLoading: readonly(isLoading),
    isLoggedIn,

    // 方法
    login,
    register,
    logout,
    refreshUser,
    validateToken,
    getAuthHeaders,
    hasPermission,
    setAuth,
    clearAuth
  }
})
