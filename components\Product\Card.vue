<template>
  <UCard class="group hover:shadow-lg transition-shadow duration-300">
    <div class="relative">
      <!-- 商品图片 -->
      <NuxtLink :to="`/products/${product.id}`" class="block aspect-square overflow-hidden rounded-lg">
        <img
          :src="product.images[0] || '/images/placeholder.jpg'"
          :alt="product.name"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
      </NuxtLink>

      <!-- 收藏按钮 -->
      <button
        @click="handleToggleFavorite"
        class="absolute top-2 right-2 p-2 bg-white/80 backdrop-blur-sm rounded-full shadow-sm hover:bg-white transition-colors"
        :class="{ 'text-red-500': isFavorited, 'text-gray-400': !isFavorited }"
      >
        <Icon :name="isFavorited ? 'heroicons:heart-solid' : 'heroicons:heart'" class="w-5 h-5" />
      </button>

      <!-- 商品标签 -->
      <div v-if="product.tags && product.tags.length > 0" class="absolute top-2 left-2">
        <UBadge
          v-for="tag in product.tags.slice(0, 2)"
          :key="tag"
          color="primary"
          variant="solid"
          size="xs"
          class="mr-1"
        >
          {{ tag }}
        </UBadge>
      </div>

      <!-- 库存状态 -->
      <div v-if="product.stock === 0" class="absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg">
        <span class="text-white font-medium">暂时缺货</span>
      </div>
    </div>

    <!-- 商品信息 -->
    <div class="p-4 space-y-3">
      <!-- 商品名称 -->
      <NuxtLink :to="`/products/${product.id}`" class="block">
        <h3 class="font-medium text-gray-900 line-clamp-2 hover:text-primary-600 transition-colors">
          {{ product.name }}
        </h3>
      </NuxtLink>

      <!-- 价格信息 -->
      <div class="flex items-baseline space-x-2">
        <span class="text-lg font-bold text-red-600"> ¥{{ product.price }} </span>
        <span
          v-if="product.originalPrice && product.originalPrice > product.price"
          class="text-sm text-gray-500 line-through"
        >
          ¥{{ product.originalPrice }}
        </span>
      </div>

      <!-- 评分和销量 -->
      <div class="flex items-center justify-between text-sm text-gray-600">
        <div class="flex items-center space-x-1">
          <Icon name="heroicons:star-solid" class="w-4 h-4 text-yellow-400" />
          <span>{{ product.rating }}</span>
          <span>({{ product.reviewCount || 0 }})</span>
        </div>
        <span>已售{{ product.sales || 0 }}件</span>
      </div>

      <!-- 商家信息 -->
      <div v-if="showMerchant && product.merchant" class="flex items-center space-x-2 text-sm text-gray-600">
        <UserAvatar
          :src="product.merchant.avatar"
          :alt="product.merchant.nickname || product.merchant.username"
          size="xs"
        />
        <span class="truncate">{{ product.merchant.nickname || product.merchant.username }}</span>
      </div>

      <!-- 操作按钮 -->
      <div class="flex space-x-2 pt-2">
        <UButton size="sm" class="flex-1" @click="handleAddToCart" :disabled="product.stock === 0">
          <Icon name="heroicons:shopping-cart" class="w-4 h-4 mr-1" />
          {{ isInCart ? '已在购物车' : '加入购物车' }}
        </UButton>

        <UButton variant="outline" size="sm" @click="handleBuyNow" :disabled="product.stock === 0"> 立购 </UButton>
      </div>
    </div>
  </UCard>
</template>

<script setup lang="ts">
import type { ProductWithRelations } from '~/types'

// 组件属性
interface Props {
  product: ProductWithRelations
  showMerchant?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showMerchant: false
})

// 状态管理
const authStore = useAuthStore()
const productsStore = useProductsStore()
const cartStore = useCartStore()

// 计算属性
const isFavorited = computed(() => productsStore.isFavorited(props.product.id))

const isInCart = computed(() => cartStore.isInCart(props.product.id))

// 切换收藏状态
const handleToggleFavorite = async () => {
  if (!authStore.isLoggedIn) {
    const toast = useToast()
    toast.add({
      title: '请先登录',
      description: '登录后即可收藏商品',
      color: 'yellow'
    })
    return navigateTo('/login')
  }

  await productsStore.toggleFavorite(props.product.id)
}

// 添加到购物车
const handleAddToCart = async () => {
  if (!authStore.isLoggedIn) {
    const toast = useToast()
    toast.add({
      title: '请先登录',
      description: '登录后即可添加到购物车',
      color: 'yellow'
    })
    return navigateTo('/login')
  }

  if (isInCart.value) {
    // 如果已在购物车，跳转到购物车页面
    return navigateTo('/cart')
  }

  await cartStore.addItem(props.product.id, 1)
}

// 立即购买
const handleBuyNow = async () => {
  if (!authStore.isLoggedIn) {
    const toast = useToast()
    toast.add({
      title: '请先登录',
      description: '登录后即可购买商品',
      color: 'yellow'
    })
    return navigateTo('/login')
  }

  await productsStore.buyNow(props.product.id, 1)
}
</script>

<style scoped>
.line-clamp-2 {
  display: box;
  line-clamp: 2;
  box-orient: vertical;
  overflow: hidden;
}
</style>
