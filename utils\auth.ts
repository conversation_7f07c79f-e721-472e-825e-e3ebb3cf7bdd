import jwt from 'jsonwebtoken'
import type { User } from '~/types'

/**
 * JWT相关工具函数
 */

// JWT载荷接口
export interface JwtPayload {
  userId: number
  username: string
  role: string
  iat?: number
  exp?: number
}

/**
 * 生成JWT token
 */
export function generateToken(user: Pick<User, 'id' | 'username' | 'role'>): string {
  const config = useRuntimeConfig()

  return jwt.sign(
    {
      userId: user.id,
      username: user.username,
      role: user.role
    },
    config.jwtSecret,
    {
      expiresIn: '7d'
    }
  )
}

/**
 * 验证JWT token
 */
export function verifyToken(token: string): JwtPayload | null {
  try {
    const config = useRuntimeConfig()
    const decoded = jwt.verify(token, config.jwtSecret) as JwtPayload
    return decoded
  } catch (error) {
    console.error('Token验证失败:', error)
    return null
  }
}

/**
 * 从请求头中提取token
 */
export function extractTokenFromHeader(event: any): string | null {
  const authorization = getHeader(event, 'authorization')

  if (!authorization) {
    return null
  }

  // Bearer token格式
  if (authorization.startsWith('Bearer ')) {
    return authorization.substring(7)
  }

  return authorization
}

/**
 * 验证用户权限
 */
export function hasPermission(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = {
    ADMIN: 3,
    MERCHANT: 2,
    USER: 1
  }

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0

  return userLevel >= requiredLevel
}

/**
 * 密码强度验证
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  if (password.length < 6) {
    errors.push('密码长度至少6位')
  }

  if (password.length > 50) {
    errors.push('密码长度不能超过50位')
  }

  if (!/[a-zA-Z]/.test(password)) {
    errors.push('密码必须包含字母')
  }

  if (!/[0-9]/.test(password)) {
    errors.push('密码必须包含数字')
  }

  // 可选：特殊字符要求
  // if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
  //   errors.push('密码必须包含特殊字符')
  // }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 生成随机字符串（用于验证码等）
 */
export function generateRandomString(length: number = 6): string {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
  let result = ''

  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }

  return result
}

/**
 * 生成数字验证码
 */
export function generateNumericCode(length: number = 6): string {
  const chars = '0123456789'
  let result = ''

  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }

  return result
}

/**
 * 验证邮箱格式
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证手机号格式（中国大陆）
 */
export function validatePhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证用户名格式
 */
export function validateUsername(username: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  if (username.length < 3) {
    errors.push('用户名长度至少3位')
  }

  if (username.length > 20) {
    errors.push('用户名长度不能超过20位')
  }

  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    errors.push('用户名只能包含字母、数字和下划线')
  }

  if (/^[0-9]/.test(username)) {
    errors.push('用户名不能以数字开头')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
