/**
 * 获取通知列表API
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  try {
    // 检查用户认证
    const currentUser = event.context.user
    if (!currentUser) {
      throw createError({
        statusCode: 401,
        statusMessage: '请先登录'
      })
    }

    // 获取查询参数
    const query = getQuery(event)
    const { page = 1, pageSize = 20, type, isRead } = query

    // 模拟通知数据
    const mockNotifications = [
      {
        id: 1,
        userId: currentUser.id,
        type: 'LIKE',
        title: '有人点赞了你的动态',
        content: '用户"科技爱好者"点赞了你分享的商品动态',
        actionUrl: '/social/posts/1',
        isRead: false,
        createdAt: new Date('2024-01-20T14:30:00'),
        updatedAt: new Date('2024-01-20T14:30:00')
      },
      {
        id: 2,
        userId: currentUser.id,
        type: 'COMMENT',
        title: '有人评论了你的动态',
        content: '用户"购物达人"评论了你的动态：确实很不错，我也想买一个！',
        actionUrl: '/social/posts/1',
        isRead: false,
        createdAt: new Date('2024-01-20T11:15:00'),
        updatedAt: new Date('2024-01-20T11:15:00')
      },
      {
        id: 3,
        userId: currentUser.id,
        type: 'FOLLOW',
        title: '有新的关注者',
        content: '用户"摄影大师"关注了你',
        actionUrl: '/users/3',
        isRead: true,
        createdAt: new Date('2024-01-19T16:45:00'),
        updatedAt: new Date('2024-01-19T16:45:00')
      },
      {
        id: 4,
        userId: currentUser.id,
        type: 'ORDER',
        title: '订单状态更新',
        content: '您的订单 #202401200001 已发货，请注意查收',
        actionUrl: '/orders/1',
        isRead: true,
        createdAt: new Date('2024-01-19T10:20:00'),
        updatedAt: new Date('2024-01-19T10:20:00')
      },
      {
        id: 5,
        userId: currentUser.id,
        type: 'PAYMENT',
        title: '支付成功',
        content: '订单 #202401190001 支付成功，金额 ¥9999.00',
        actionUrl: '/orders/2',
        isRead: true,
        createdAt: new Date('2024-01-18T20:30:00'),
        updatedAt: new Date('2024-01-18T20:30:00')
      },
      {
        id: 6,
        userId: currentUser.id,
        type: 'SYSTEM',
        title: '系统维护通知',
        content: '系统将于今晚23:00-01:00进行维护升级，期间可能影响部分功能使用',
        actionUrl: null,
        isRead: true,
        createdAt: new Date('2024-01-17T15:00:00'),
        updatedAt: new Date('2024-01-17T15:00:00')
      },
      {
        id: 7,
        userId: currentUser.id,
        type: 'PROMOTION',
        title: '限时优惠活动',
        content: '春节特惠活动开始啦！全场商品最高立减1000元，快来抢购吧！',
        actionUrl: '/promotions/spring-sale',
        isRead: false,
        createdAt: new Date('2024-01-16T09:00:00'),
        updatedAt: new Date('2024-01-16T09:00:00')
      }
    ]

    // 应用筛选条件
    let filteredNotifications = mockNotifications

    // 按类型筛选
    if (type && type !== 'all') {
      filteredNotifications = filteredNotifications.filter(notification => notification.type === type)
    }

    // 按已读状态筛选
    if (isRead !== undefined) {
      const isReadBool = isRead === 'true'
      filteredNotifications = filteredNotifications.filter(notification => notification.isRead === isReadBool)
    }

    // 按时间倒序排序
    filteredNotifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

    // 应用分页
    const pageNum = parseInt(page.toString())
    const pageSizeNum = parseInt(pageSize.toString())
    const total = filteredNotifications.length
    const totalPages = Math.ceil(total / pageSizeNum)
    const startIndex = (pageNum - 1) * pageSizeNum
    const endIndex = startIndex + pageSizeNum
    const items = filteredNotifications.slice(startIndex, endIndex)

    return createSuccessResponse(
      {
        items,
        total,
        page: pageNum,
        pageSize: pageSizeNum,
        totalPages
      },
      '获取通知列表成功'
    )
  } catch (error: any) {
    console.error('获取通知列表失败:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || '获取通知列表失败'
    })
  }
})
