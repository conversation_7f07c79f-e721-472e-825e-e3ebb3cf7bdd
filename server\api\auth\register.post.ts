import bcrypt from 'bcryptjs'
import { z } from 'zod'
import { defineApiHandler } from '~/utils/api'

/**
 * 用户注册接口
 * POST /api/auth/register
 */

// 请求数据验证schema
const registerSchema = z.object({
  username: z.string().min(3, '用户名至少3个字符').max(20, '用户名最多20个字符'),
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(6, '密码至少6个字符').max(50, '密码最多50个字符'),
  phone: z.string().optional(),
  captcha: z.string().min(4, '验证码至少4位').max(6, '验证码最多6位')
})

export default defineApiHandler(async event => {
  // 只允许POST请求
  assertMethod(event, 'POST')

  // 获取请求体数据
  const body = await readBody(event)

  // 验证请求数据
  const validatedData = registerSchema.parse(body)

  // 验证用户名格式
  const usernameValidation = validateUsername(validatedData.username)
  if (!usernameValidation.isValid) {
    throw new ValidationError('用户名格式不正确', usernameValidation.errors)
  }

  // 验证密码强度
  const passwordValidation = validatePasswordStrength(validatedData.password)
  if (!passwordValidation.isValid) {
    throw new ValidationError('密码强度不足', passwordValidation.errors)
  }

  // 检查用户名和邮箱是否已存在
  const existingUser = await prisma.user.findFirst({
    where: {
      OR: [{ username: validatedData.username }, { email: validatedData.email }]
    }
  })

  if (existingUser) {
    if (existingUser.username === validatedData.username) {
      throw new ConflictError('用户名已存在')
    }
    if (existingUser.email === validatedData.email) {
      throw new ConflictError('邮箱已被注册')
    }
  }

  // 加密密码
  const saltRounds = 12
  const hashedPassword = await bcrypt.hash(validatedData.password, saltRounds)

  // 创建用户记录
  const newUser = await prisma.user.create({
    data: {
      username: validatedData.username,
      email: validatedData.email,
      password: hashedPassword,
      phone: validatedData.phone,
      role: 'USER',
      status: 'ACTIVE'
    },
    select: {
      id: true,
      username: true,
      email: true,
      phone: true,
      avatar: true,
      nickname: true,
      role: true,
      status: true,
      createdAt: true,
      updatedAt: true
    }
  })

  // 生成JWT token
  const token = generateToken({
    id: newUser.id,
    username: newUser.username,
    role: newUser.role
  })

  // 记录注册日志
  await logRegister(newUser.id, event, {
    registerMethod: 'email'
  })

  return createSuccessResponse(
    {
      user: newUser,
      token
    },
    '注册成功'
  )
})
