/**
 * 检查邮箱是否可用
 * GET /api/auth/check-email
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  const query = getQuery(event)

  // 获取邮箱参数
  const email = query.email as string
  if (!email || email.trim().length === 0) {
    throw new ValidationError('邮箱不能为空')
  }

  // 验证邮箱格式
  const emailValidation = validateEmail(email.trim())
  if (!emailValidation.isValid) {
    return createSuccessResponse(
      {
        available: false,
        reason: 'format',
        message: emailValidation.errors[0] || '邮箱格式不正确'
      },
      '邮箱格式不正确'
    )
  }

  try {
    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email: email.trim().toLowerCase() },
      select: { id: true }
    })

    const isAvailable = !existingUser

    return createSuccessResponse(
      {
        available: isAvailable,
        reason: isAvailable ? null : 'exists',
        message: isAvailable ? '邮箱可用' : '邮箱已被使用'
      },
      isAvailable ? '邮箱可用' : '邮箱已被使用'
    )
  } catch (error) {
    console.error('检查邮箱失败:', error)
    throw new InternalServerError('检查邮箱失败')
  }
})

/**
 * 验证邮箱格式
 */
function validateEmail(email: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  // 基本格式检查
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    errors.push('请输入有效的邮箱地址')
    return { isValid: false, errors }
  }

  // 长度检查
  if (email.length > 254) {
    errors.push('邮箱地址过长')
  }

  // 本地部分长度检查
  const [localPart] = email.split('@')
  if (localPart.length > 64) {
    errors.push('邮箱用户名部分过长')
  }

  // 检查是否包含危险字符
  const dangerousChars = ['<', '>', '"', "'", '&', '\\', '/']
  if (dangerousChars.some(char => email.includes(char))) {
    errors.push('邮箱地址包含非法字符')
  }

  // 检查是否是一次性邮箱域名（可选）
  const disposableEmailDomains = [
    '10minutemail.com',
    'tempmail.org',
    'guerrillamail.com',
    'mailinator.com',
    'yopmail.com',
    'temp-mail.org'
  ]

  const domain = email.split('@')[1]?.toLowerCase()
  if (domain && disposableEmailDomains.includes(domain)) {
    errors.push('不支持临时邮箱，请使用常用邮箱注册')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
