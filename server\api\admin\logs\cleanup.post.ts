/**
 * 清理过期日志
 * POST /api/admin/logs/cleanup
 */

import { z } from 'zod'
import { defineApiHandler } from '~/utils/api'

// 请求数据验证schema
const cleanupSchema = z.object({
  daysToKeep: z.number().int().min(1).max(365).optional().default(90),
  logTypes: z
    .array(z.enum(['search', 'user', 'all']))
    .optional()
    .default(['all'])
})

export default defineApiHandler(async event => {
  // 检查管理员权限
  const user = event.context.user
  if (!user || user.role !== 'admin') {
    throw new AuthorizationError('需要管理员权限')
  }

  const body = await readBody(event)
  const validatedData = cleanupSchema.parse(body)

  try {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - validatedData.daysToKeep)

    let searchLogsDeleted = 0
    let userLogsDeleted = 0

    // 根据指定的日志类型进行清理
    if (validatedData.logTypes.includes('all') || validatedData.logTypes.includes('search')) {
      const searchResult = await prisma.searchLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          }
        }
      })
      searchLogsDeleted = searchResult.count
    }

    if (validatedData.logTypes.includes('all') || validatedData.logTypes.includes('user')) {
      const userResult = await prisma.userLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          }
        }
      })
      userLogsDeleted = userResult.count
    }

    const result = {
      daysToKeep: validatedData.daysToKeep,
      cutoffDate,
      searchLogsDeleted,
      userLogsDeleted,
      totalDeleted: searchLogsDeleted + userLogsDeleted
    }

    console.log(`日志清理完成: 搜索日志 ${searchLogsDeleted} 条, 用户日志 ${userLogsDeleted} 条`)

    return createSuccessResponse(result, '日志清理完成')
  } catch (error) {
    console.error('清理日志失败:', error)
    throw new InternalServerError('清理日志失败')
  }
})
