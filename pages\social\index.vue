<template>
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto space-y-6">
      <!-- 发布动态 -->
      <UCard v-if="authStore.isLoggedIn" class="p-6">
        <div class="flex items-start space-x-4">
          <UserAvatar
            :src="authStore.user?.avatar"
            :alt="authStore.user?.nickname || authStore.user?.username"
            size="md"
          />
          <div class="flex-1">
            <UTextarea v-model="newPostContent" placeholder="分享你的想法..." :rows="3" class="mb-4" />

            <!-- 图片上传 -->
            <div v-if="uploadedImages.length > 0" class="mb-4">
              <div class="grid grid-cols-3 gap-2">
                <div
                  v-for="(image, index) in uploadedImages"
                  :key="index"
                  class="relative aspect-square bg-gray-100 rounded-lg overflow-hidden"
                >
                  <img :src="image" alt="上传的图片" class="w-full h-full object-cover" />
                  <button
                    @click="removeImage(index)"
                    class="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full text-xs"
                  >
                    <Icon name="heroicons:x-mark" class="w-3 h-3" />
                  </button>
                </div>
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <UButton variant="ghost" size="sm" @click="triggerImageUpload" :disabled="uploadedImages.length >= 9">
                  <Icon name="heroicons:photo" class="w-4 h-4 mr-1" />
                  图片
                </UButton>
                <UButton variant="ghost" size="sm" @click="showProductSelector = true">
                  <Icon name="heroicons:shopping-bag" class="w-4 h-4 mr-1" />
                  商品
                </UButton>
              </div>

              <UButton @click="publishPost" :disabled="!newPostContent.trim()" :loading="isPublishing"> 发布 </UButton>
            </div>
          </div>
        </div>

        <!-- 隐藏的文件输入 -->
        <input ref="imageInput" type="file" accept="image/*" multiple class="hidden" @change="handleImageUpload" />
      </UCard>

      <!-- 动态筛选 -->
      <div class="flex items-center space-x-4">
        <UButton
          v-for="filter in postFilters"
          :key="filter.key"
          :variant="activeFilter === filter.key ? 'solid' : 'ghost'"
          size="sm"
          @click="setFilter(filter.key)"
        >
          {{ filter.label }}
        </UButton>
      </div>

      <!-- 动态列表 -->
      <div class="space-y-6">
        <!-- 加载状态 -->
        <div v-if="socialStore.isLoading && socialStore.posts.length === 0" class="space-y-4">
          <div v-for="i in 3" :key="i" class="animate-pulse">
            <UCard class="p-6">
              <div class="flex items-start space-x-4">
                <div class="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div class="flex-1 space-y-2">
                  <div class="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </UCard>
          </div>
        </div>

        <!-- 动态卡片 -->
        <PostCard
          v-for="post in socialStore.posts"
          :key="post.id"
          :post="post as any"
          @like="socialStore.toggleLike"
          @comment="handleComment"
          @share="handleShare"
          @delete="handleDelete"
          @report="handleReport"
        />

        <!-- 空状态 -->
        <div v-if="!socialStore.isLoading && socialStore.posts.length === 0" class="text-center py-12">
          <Icon name="heroicons:document-text" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-semibold text-gray-600 mb-2">暂无动态</h3>
          <p class="text-gray-500 mb-6">还没有人发布动态，快来发布第一条吧！</p>
          <UButton v-if="!authStore.isLoggedIn" @click="navigateTo('/login')"> 登录发布动态 </UButton>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="socialStore.pagination.totalPages > 1" class="flex justify-center">
        <UPagination
          v-model="socialStore.pagination.page"
          :page-count="socialStore.pagination.pageSize"
          :total="socialStore.pagination.total"
          @update:model-value="handlePageChange"
        />
      </div>
    </div>

    <!-- 商品选择器模态框 -->
    <UModal v-model="showProductSelector">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">选择要分享的商品</h3>
        </template>

        <div class="space-y-4">
          <UInput v-model="productSearchQuery" placeholder="搜索商品..." @input="searchProducts" />

          <div class="max-h-96 overflow-y-auto space-y-2">
            <!-- 加载状态 -->
            <div v-if="isSearchingProducts" class="flex items-center justify-center py-8">
              <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
              <span class="ml-2 text-gray-600">搜索中...</span>
            </div>

            <!-- 搜索结果 -->
            <div
              v-for="product in searchedProducts"
              :key="product.id"
              @click="selectProduct(product)"
              class="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer"
            >
              <img
                :src="product.images[0] || '/images/placeholder.jpg'"
                :alt="product.name"
                class="w-12 h-12 object-cover rounded"
              />
              <div class="flex-1">
                <h4 class="font-medium text-gray-900">{{ product.name }}</h4>
                <p class="text-sm text-red-600">¥{{ product.price }}</p>
              </div>
            </div>

            <!-- 无搜索结果 -->
            <div
              v-if="!isSearchingProducts && productSearchQuery.trim() && searchedProducts.length === 0"
              class="text-center py-8 text-gray-500"
            >
              <Icon name="heroicons:magnifying-glass" class="w-12 h-12 mx-auto mb-2 text-gray-300" />
              <p>未找到相关商品</p>
              <p class="text-sm">尝试使用其他关键词搜索</p>
            </div>
          </div>
        </div>
      </UCard>
    </UModal>

    <!-- 评论模态框 -->
    <UModal v-model="showCommentModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">发表评论</h3>
        </template>

        <div class="space-y-4">
          <UTextarea v-model="commentContent" placeholder="写下你的评论..." :rows="4" />

          <div class="flex justify-end space-x-3">
            <UButton variant="ghost" @click="showCommentModal = false"> 取消 </UButton>
            <UButton @click="submitComment" :disabled="!commentContent.trim()" :loading="isCommenting">
              发表评论
            </UButton>
          </div>
        </div>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import type { Post, Product } from '~/types'

// 页面SEO
useHead({
  title: '社交动态 - 社交购物网站',
  meta: [{ name: 'description', content: '发现和分享有趣的商品，与朋友互动交流' }]
})

// 状态管理
const authStore = useAuthStore()
const socialStore = useSocialStore()
const toast = useToast()

// 响应式数据
const newPostContent = ref('')
const uploadedImages = ref<string[]>([])
const isPublishing = ref(false)
const activeFilter = ref('all')
const showProductSelector = ref(false)
const showCommentModal = ref(false)
const commentContent = ref('')
const commentingPostId = ref<number | null>(null)
const isCommenting = ref(false)
const productSearchQuery = ref('')
const searchedProducts = ref<Product[]>([])
const selectedProduct = ref<Product | null>(null)
const isSearchingProducts = ref(false)

// 文件输入引用
const imageInput = ref<HTMLInputElement>()

// 动态筛选选项
const postFilters = [
  { key: 'all', label: '全部' },
  { key: 'following', label: '关注' },
  { key: 'TEXT', label: '文字' },
  { key: 'IMAGE', label: '图片' },
  { key: 'PRODUCT_SHARE', label: '商品分享' }
]

// 设置筛选条件
const setFilter = (filterKey: string) => {
  activeFilter.value = filterKey
  socialStore.pagination.page = 1
  fetchPosts()
}

// 获取动态列表
const fetchPosts = async () => {
  const params: Record<string, any> = {}

  if (activeFilter.value === 'following') {
    params.following = true
  } else if (activeFilter.value !== 'all') {
    params.type = activeFilter.value
  }

  await socialStore.fetchPosts(params)
}

// 处理分页
const handlePageChange = (page: number) => {
  socialStore.pagination.page = page
  fetchPosts()
}

// 触发图片上传
const triggerImageUpload = () => {
  imageInput.value?.click()
}

// 处理图片上传
const handleImageUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files

  if (!files) return

  try {
    // 显示上传进度
    toast.add({
      title: '正在上传图片...',
      description: `上传 ${files.length} 张图片`,
      color: 'blue'
    })

    // 创建FormData
    const formData = new FormData()
    for (const file of Array.from(files)) {
      if (uploadedImages.value.length >= 9) break
      formData.append('files', file)
    }

    // 调用上传API
    const response = await $fetch<any>('/api/upload/images', {
      method: 'POST',
      body: formData
    })

    // 添加上传成功的图片URL
    if (response.data?.files) {
      uploadedImages.value.push(...response.data.files)
    }

    // 显示成功提示
    toast.add({
      title: '图片上传成功',
      description: `成功上传 ${response.data?.count || 0} 张图片`,
      color: 'green'
    })
  } catch (error: any) {
    console.error('图片上传失败:', error)

    // 显示错误提示
    toast.add({
      title: '图片上传失败',
      description: error.data?.message || '请稍后重试',
      color: 'red'
    })
  }

  // 清空input
  target.value = ''
}

// 移除图片
const removeImage = (index: number) => {
  uploadedImages.value.splice(index, 1)
}

// 搜索防抖定时器
let searchTimer: NodeJS.Timeout | null = null

// 搜索商品
const searchProducts = async () => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  // 如果搜索词为空，立即清空结果
  if (!productSearchQuery.value.trim()) {
    searchedProducts.value = []
    return
  }

  // 设置防抖延迟
  searchTimer = setTimeout(async () => {
    try {
      isSearchingProducts.value = true
      const response = await $fetch<any>('/api/products/search', {
        params: {
          q: productSearchQuery.value,
          page: 1,
          pageSize: 10,
          sortBy: 'sales',
          sortOrder: 'desc'
        }
      })

      searchedProducts.value = response.data?.items || []
    } catch (error: any) {
      console.error('搜索商品失败:', error)
      toast.add({
        title: '搜索失败',
        description: error.data?.message || '请稍后重试',
        color: 'red'
      })
      searchedProducts.value = []
    } finally {
      isSearchingProducts.value = false
    }
  }, 300) // 300ms 防抖延迟
}

// 选择商品
const selectProduct = (product: Product) => {
  selectedProduct.value = product
  showProductSelector.value = false

  // 自动填充商品分享内容
  if (!newPostContent.value.trim()) {
    newPostContent.value = `推荐一个不错的商品：${product.name}`
  }
}

// 发布动态
const publishPost = async () => {
  if (!newPostContent.value.trim()) return

  try {
    isPublishing.value = true

    const postData: any = {
      content: newPostContent.value,
      type: 'TEXT'
    }

    if (uploadedImages.value.length > 0) {
      postData.images = uploadedImages.value
      postData.type = 'IMAGE'
    }

    if (selectedProduct.value) {
      postData.productId = selectedProduct.value.id
      postData.type = 'PRODUCT_SHARE'
    }

    await socialStore.createPost(postData)

    // 重置表单
    newPostContent.value = ''
    uploadedImages.value = []
    selectedProduct.value = null
  } catch (error) {
    console.error('发布动态失败:', error)
  } finally {
    isPublishing.value = false
  }
}

// 处理评论
const handleComment = (post: Post) => {
  commentingPostId.value = post.id
  commentContent.value = ''
  showCommentModal.value = true
}

// 提交评论
const submitComment = async () => {
  if (!commentContent.value.trim() || !commentingPostId.value) return

  try {
    isCommenting.value = true
    await socialStore.commentPost(commentingPostId.value, commentContent.value)
    showCommentModal.value = false
  } catch (error) {
    console.error('评论失败:', error)
  } finally {
    isCommenting.value = false
  }
}

// 处理分享
const handleShare = async (post: Post) => {
  try {
    // 检查是否支持Web Share API
    if (navigator.share) {
      await navigator.share({
        title: `${post.user.nickname || post.user.username}的动态`,
        text: post.content,
        url: `${window.location.origin}/social/posts/${post.id}`
      })
    } else {
      // 降级到复制链接
      const shareUrl = `${window.location.origin}/social/posts/${post.id}`
      await navigator.clipboard.writeText(shareUrl)
      toast.add({
        title: '链接已复制',
        description: '动态链接已复制到剪贴板',
        color: 'green'
      })
    }
  } catch (error: any) {
    console.error('分享失败:', error)
    // 如果分享被取消或失败，不显示错误提示
    if (error?.name !== 'AbortError') {
      toast.add({
        title: '分享失败',
        description: '请稍后重试',
        color: 'red'
      })
    }
  }
}

// 处理删除
const handleDelete = async (post: Post) => {
  if (confirm('确定要删除这条动态吗？')) {
    await socialStore.deletePost(post.id)
  }
}

// 处理举报
const handleReport = async (post: Post) => {
  const reason = prompt('请输入举报原因：')
  if (reason) {
    await socialStore.reportPost(post.id, reason)
  }
}

// 页面加载时获取动态列表
onMounted(() => {
  fetchPosts()
})
</script>
