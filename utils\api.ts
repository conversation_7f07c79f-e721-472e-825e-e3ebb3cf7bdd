import type { ApiResponse, PaginatedResponse } from '~/types'

/**
 * API响应工具函数
 */

/**
 * 成功响应
 */
export function createSuccessResponse<T>(data: T, message: string = '操作成功'): ApiResponse<T> {
  return {
    success: true,
    message,
    data
  }
}

/**
 * 错误响应
 */
export function createErrorResponse(message: string = '操作失败', errors?: any[]): ApiResponse {
  return {
    success: false,
    message,
    errors
  }
}

/**
 * 分页响应
 */
export function createPaginatedResponse<T>(
  items: T[],
  total: number,
  page: number,
  pageSize: number,
  message: string = '获取成功'
): ApiResponse<PaginatedResponse<T>> {
  const totalPages = Math.ceil(total / pageSize)

  return createSuccessResponse(
    {
      items,
      total,
      page,
      pageSize,
      totalPages
    },
    message
  )
}

/**
 * API错误类
 */
export class ApiError extends Error {
  statusCode: number
  errors?: any[]

  constructor(message: string, statusCode: number = 500, errors?: any[]) {
    super(message)
    this.name = 'ApiError'
    this.statusCode = statusCode
    this.errors = errors
  }
}

/**
 * 验证错误
 */
export class ValidationError extends ApiError {
  constructor(message: string = '请求数据验证失败', errors?: any[]) {
    super(message, 400, errors)
    this.name = 'ValidationError'
  }
}

/**
 * 认证错误
 */
export class AuthenticationError extends ApiError {
  constructor(message: string = '认证失败') {
    super(message, 401)
    this.name = 'AuthenticationError'
  }
}

/**
 * 授权错误
 */
export class AuthorizationError extends ApiError {
  constructor(message: string = '权限不足') {
    super(message, 403)
    this.name = 'AuthorizationError'
  }
}

/**
 * 资源未找到错误
 */
export class NotFoundError extends ApiError {
  constructor(message: string = '资源未找到') {
    super(message, 404)
    this.name = 'NotFoundError'
  }
}

/**
 * 冲突错误
 */
export class ConflictError extends ApiError {
  constructor(message: string = '资源冲突') {
    super(message, 409)
    this.name = 'ConflictError'
  }
}

/**
 * 业务逻辑错误
 */
export class BusinessError extends ApiError {
  constructor(message: string, statusCode: number = 422) {
    super(message, statusCode)
    this.name = 'BusinessError'
  }
}

/**
 * 服务器内部错误
 */
export class InternalServerError extends ApiError {
  constructor(message: string = '服务器内部错误') {
    super(message, 500)
    this.name = 'InternalServerError'
  }
}

/**
 * 处理API错误
 */
export function handleApiError(error: any): never {
  console.error('API错误:', error)

  if (error instanceof ApiError) {
    throw createError({
      statusCode: error.statusCode,
      statusMessage: error.message,
      data: error.errors
    })
  }

  // Zod验证错误
  if (error.name === 'ZodError') {
    const validationErrors = error.errors.map((err: any) => ({
      field: err.path.join('.'),
      message: err.message
    }))

    throw createError({
      statusCode: 400,
      statusMessage: '请求数据验证失败',
      data: validationErrors
    })
  }

  // Prisma错误
  if (error.code) {
    switch (error.code) {
      case 'P2002':
        throw createError({
          statusCode: 409,
          statusMessage: '数据已存在，违反唯一约束'
        })
      case 'P2025':
        throw createError({
          statusCode: 404,
          statusMessage: '记录未找到'
        })
      default:
        throw createError({
          statusCode: 500,
          statusMessage: '数据库操作失败'
        })
    }
  }

  // 默认错误
  throw createError({
    statusCode: 500,
    statusMessage: error.message || '服务器内部错误'
  })
}

/**
 * API路由包装器
 */
export function defineApiHandler<T = any>(handler: (event: any) => Promise<T>) {
  return defineEventHandler(async event => {
    try {
      const result = await handler(event)
      return result
    } catch (error) {
      handleApiError(error)
    }
  })
}

/**
 * 分页参数解析
 */
export function parsePaginationQuery(query: any) {
  const page = Math.max(1, parseInt(query.page) || 1)
  const pageSize = Math.min(100, Math.max(1, parseInt(query.pageSize) || 10))
  const skip = (page - 1) * pageSize

  return {
    page,
    pageSize,
    skip,
    take: pageSize
  }
}

/**
 * 排序参数解析
 */
export function parseSortQuery(query: any, allowedFields: string[]) {
  const sortBy = query.sortBy || 'createdAt'
  const sortOrder = query.sortOrder === 'asc' ? 'asc' : 'desc'

  // 验证排序字段
  if (!allowedFields.includes(sortBy)) {
    return { createdAt: 'desc' as const }
  }

  return {
    [sortBy]: sortOrder as 'asc' | 'desc'
  }
}

/**
 * 搜索参数解析
 */
export function parseSearchQuery(query: any) {
  const keyword = query.keyword?.trim() || ''
  const filters: Record<string, any> = {}

  // 解析其他过滤条件
  Object.keys(query).forEach(key => {
    if (key.startsWith('filter_') && query[key]) {
      const filterKey = key.replace('filter_', '')
      filters[filterKey] = query[key]
    }
  })

  return {
    keyword,
    filters
  }
}
