<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 加载状态 -->
    <div v-if="productsStore.isLoading" class="flex justify-center items-center min-h-96">
      <div class="loading w-8 h-8" />
    </div>

    <!-- 商品详情 -->
    <div v-else-if="product" class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 商品图片 -->
      <div class="space-y-4">
        <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
          <img :src="currentImage" :alt="product.name" class="w-full h-full object-cover" />
        </div>

        <!-- 图片缩略图 -->
        <div v-if="product.images.length > 1" class="flex space-x-2 overflow-x-auto">
          <button
            v-for="(image, index) in product.images"
            :key="index"
            @click="currentImage = image"
            class="flex-shrink-0 w-16 h-16 bg-gray-100 rounded-lg overflow-hidden border-2 transition-colors"
            :class="currentImage === image ? 'border-primary-500' : 'border-transparent'"
          >
            <img :src="image" :alt="`${product.name} ${index + 1}`" class="w-full h-full object-cover" />
          </button>
        </div>
      </div>

      <!-- 商品信息 -->
      <div class="space-y-6">
        <!-- 基本信息 -->
        <div>
          <h1 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
            {{ product.name }}
          </h1>

          <!-- 价格 -->
          <div class="flex items-baseline space-x-2 mb-4">
            <span class="text-3xl font-bold text-red-600"> ¥{{ product.price }} </span>
            <span
              v-if="product.originalPrice && product.originalPrice > product.price"
              class="text-lg text-gray-500 line-through"
            >
              ¥{{ product.originalPrice }}
            </span>
          </div>

          <!-- 评分和销量 -->
          <div class="flex items-center space-x-4 text-sm text-gray-600 mb-4">
            <div class="flex items-center space-x-1">
              <Icon name="heroicons:star-solid" class="w-4 h-4 text-yellow-400" />
              <span>{{ product.rating }}</span>
              <span>({{ product.reviewCount }}条评价)</span>
            </div>
            <div>已售{{ product.sales }}件</div>
          </div>

          <!-- 商家信息 -->
          <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg mb-4">
            <UserAvatar
              :src="product.merchant?.avatar"
              :alt="product.merchant?.nickname || product.merchant?.username"
              size="sm"
              :role="product.merchant?.role"
              show-role-badge
            />
            <div class="flex-1">
              <div class="font-medium">{{ product.merchant?.nickname || product.merchant?.username }}</div>
              <div class="text-sm text-gray-500">
                {{ product.merchant?.productsCount }}件商品 · {{ product.merchant?.followersCount }}粉丝
              </div>
            </div>
            <UButton variant="outline" size="sm" @click="followMerchant" :disabled="!authStore.isLoggedIn">
              {{ isFollowingMerchant ? '已关注' : '关注' }}
            </UButton>
          </div>
        </div>

        <!-- 商品规格 -->
        <div v-if="product.specifications" class="space-y-3">
          <h3 class="font-semibold text-gray-900">商品规格</h3>
          <div class="grid grid-cols-2 gap-2 text-sm">
            <div
              v-for="[key, value] in Object.entries(product.specifications)"
              :key="key"
              class="flex justify-between py-1"
            >
              <span class="text-gray-500">{{ key }}：</span>
              <span class="text-gray-900">{{ value }}</span>
            </div>
          </div>
        </div>

        <!-- 优惠券 -->
        <div v-if="availableCoupons.length > 0" class="bg-red-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <h3 class="font-medium text-red-800">可领取优惠券</h3>
            <UButton variant="ghost" size="sm" @click="showAllCoupons = !showAllCoupons">
              {{ showAllCoupons ? '收起' : `查看全部${availableCoupons.length}张` }}
              <Icon :name="showAllCoupons ? 'heroicons:chevron-up' : 'heroicons:chevron-down'" class="w-4 h-4 ml-1" />
            </UButton>
          </div>

          <div class="space-y-2">
            <div
              v-for="(coupon, index) in showAllCoupons ? availableCoupons : availableCoupons.slice(0, 2)"
              :key="coupon.id"
              class="flex items-center justify-between bg-white rounded-lg p-3 border border-red-200"
            >
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <span
                    class="px-2 py-1 text-xs rounded"
                    :class="coupon.type === 'SHARED' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'"
                  >
                    {{ coupon.type === 'SHARED' ? '同享券' : '互斥券' }}
                  </span>
                  <span class="font-medium text-red-600">
                    {{ coupon.discountType === 'FIXED' ? `¥${coupon.discountValue}` : `${coupon.discountValue}%` }}
                  </span>
                </div>
                <p class="text-sm text-gray-600 mt-1">{{ coupon.name }}</p>
                <p class="text-xs text-gray-500">
                  满{{ coupon.minOrderAmount }}元可用 · 有效期至{{ formatDate(coupon.endTime) }}
                </p>
              </div>

              <UButton
                v-if="!coupon.isReceived"
                size="sm"
                :loading="claimingCouponId === coupon.id"
                @click="claimCoupon(coupon.id)"
              >
                领取
              </UButton>
              <span v-else class="text-sm text-gray-500">已领取</span>
            </div>
          </div>
        </div>

        <!-- 数量选择 -->
        <div class="space-y-3">
          <h3 class="font-semibold text-gray-900">购买数量</h3>
          <div class="flex items-center space-x-4">
            <div class="flex items-center border rounded-lg">
              <UButton variant="ghost" size="sm" @click="decreaseQuantity" :disabled="quantity <= 1">
                <Icon name="heroicons:minus" class="w-4 h-4" />
              </UButton>
              <input
                v-model.number="quantity"
                type="number"
                min="1"
                :max="product.stock"
                class="w-16 text-center border-0 focus:ring-0"
              />
              <UButton variant="ghost" size="sm" @click="increaseQuantity" :disabled="quantity >= product.stock">
                <Icon name="heroicons:plus" class="w-4 h-4" />
              </UButton>
            </div>
            <span class="text-sm text-gray-500">库存{{ product.stock }}件</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="space-y-3">
          <div class="flex space-x-3">
            <UButton size="lg" class="flex-1" @click="handleBuyNow" :disabled="product.stock === 0">
              {{ product.stock === 0 ? '暂时缺货' : '立即购买' }}
            </UButton>
            <UButton
              variant="outline"
              size="lg"
              class="flex-1"
              @click="handleAddToCart"
              :disabled="product.stock === 0"
            >
              <Icon name="heroicons:shopping-cart" class="w-5 h-5 mr-2" />
              加入购物车
            </UButton>
          </div>

          <div class="flex space-x-3">
            <UButton variant="ghost" size="lg" class="flex-1" @click="handleToggleFavorite">
              <Icon
                :name="isFavorited ? 'heroicons:heart-solid' : 'heroicons:heart'"
                class="w-5 h-5 mr-2"
                :class="isFavorited ? 'text-red-500' : ''"
              />
              {{ isFavorited ? '已收藏' : '收藏' }}
            </UButton>
            <UButton variant="ghost" size="lg" class="flex-1" @click="handleShare">
              <Icon name="heroicons:share" class="w-5 h-5 mr-2" />
              分享
            </UButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 商品详情和评价 -->
    <div v-if="product" class="mt-12">
      <UTabs :items="tabs" class="w-full">
        <template #item="{ item }">
          <div class="py-6">
            <!-- 商品详情 -->
            <div v-if="item.key === 'description'" class="prose max-w-none">
              <div class="whitespace-pre-wrap">{{ product.description }}</div>
            </div>

            <!-- 商品评价 -->
            <div v-else-if="item.key === 'reviews'" class="space-y-6">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">商品评价 ({{ product.reviewCount }})</h3>
                <UButton v-if="authStore.isLoggedIn" variant="outline" @click="showReviewModal = true">
                  写评价
                </UButton>
              </div>

              <!-- 评价列表 -->
              <div class="space-y-4">
                <div v-for="review in product.reviews" :key="review.id" class="border-b pb-4 last:border-b-0">
                  <div class="flex items-start space-x-3">
                    <UserAvatar
                      :src="review.user.avatar"
                      :alt="review.user.nickname || review.user.username"
                      size="sm"
                    />
                    <div class="flex-1">
                      <div class="flex items-center space-x-2 mb-1">
                        <span class="font-medium">{{ review.user.nickname || review.user.username }}</span>
                        <div class="flex items-center">
                          <Icon
                            v-for="i in 5"
                            :key="i"
                            name="heroicons:star-solid"
                            class="w-4 h-4"
                            :class="i <= review.rating ? 'text-yellow-400' : 'text-gray-300'"
                          />
                        </div>
                      </div>
                      <p class="text-gray-700 mb-2">{{ review.content }}</p>
                      <div v-if="review.images.length" class="flex space-x-2">
                        <img
                          v-for="image in review.images"
                          :key="image"
                          :src="image"
                          alt="评价图片"
                          class="w-16 h-16 object-cover rounded"
                        />
                      </div>
                      <div class="text-xs text-gray-500 mt-2">
                        {{ formatDate(review.createdAt) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 相关推荐 -->
            <div v-else-if="item.key === 'related'" class="space-y-6">
              <h3 class="text-lg font-semibold">相关推荐</h3>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <ProductCard
                  v-for="relatedProduct in product.relatedProducts"
                  :key="relatedProduct.id"
                  :product="relatedProduct"
                />
              </div>
            </div>
          </div>
        </template>
      </UTabs>
    </div>

    <!-- 评价模态框 -->
    <UModal v-model="showReviewModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">写评价</h3>
        </template>

        <div class="space-y-4">
          <!-- 评分 -->
          <div>
            <label class="block text-sm font-medium mb-2">评分</label>
            <div class="flex items-center space-x-1">
              <button v-for="i in 5" :key="i" @click="reviewForm.rating = i" class="p-1">
                <Icon
                  name="heroicons:star-solid"
                  class="w-6 h-6"
                  :class="i <= reviewForm.rating ? 'text-yellow-400' : 'text-gray-300'"
                />
              </button>
            </div>
          </div>

          <!-- 评价内容 -->
          <div>
            <label class="block text-sm font-medium mb-2">评价内容</label>
            <UTextarea v-model="reviewForm.content" placeholder="分享您的使用体验..." :rows="4" />
          </div>

          <!-- 匿名评价选项 -->
          <div class="flex items-center space-x-2">
            <UCheckbox v-model="reviewForm.anonymous" id="anonymous-review" />
            <label for="anonymous-review" class="text-sm text-gray-600"> 匿名评价（不显示用户名） </label>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-end space-x-3">
            <UButton variant="ghost" @click="showReviewModal = false"> 取消 </UButton>
            <UButton @click="submitReview" :loading="isSubmittingReview"> 提交评价 </UButton>
          </div>
        </div>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import type { ProductWithRelations } from '~/types'

// 页面参数
const route = useRoute()
const productId = parseInt(route.params.id as string)

// 状态管理
const authStore = useAuthStore()
const productsStore = useProductsStore()
const cartStore = useCartStore()
const toast = useToast()

// 响应式数据
const product = ref<ProductWithRelations | null>(null)
const currentImage = ref('')
const quantity = ref(1)
const showReviewModal = ref(false)
const isSubmittingReview = ref(false)
const isFollowingMerchant = ref(false)

// 优惠券相关状态
const availableCoupons = ref<any[]>([])
const showAllCoupons = ref(false)
const claimingCouponId = ref<number | null>(null)

// 评价表单
const reviewForm = reactive({
  rating: 5,
  content: '',
  anonymous: false
})

// 标签页配置
const tabs = [
  { key: 'description', label: '商品详情' },
  { key: 'reviews', label: '商品评价' },
  { key: 'related', label: '相关推荐' }
]

// 计算属性
const isFavorited = computed(() => (product.value ? productsStore.isFavorited(product.value.id) : false))

// 页面SEO
useHead(() => ({
  title: product.value ? `${product.value.name} - 社交购物网站` : '商品详情',
  meta: [{ name: 'description', content: product.value?.description || '查看商品详情' }]
}))

// 数量操作
const increaseQuantity = () => {
  if (product.value && quantity.value < product.value.stock) {
    quantity.value++
  }
}

const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

// 立即购买
const handleBuyNow = async () => {
  if (!product.value) return
  await productsStore.buyNow(product.value.id, quantity.value)
}

// 加入购物车
const handleAddToCart = async () => {
  if (!product.value) return
  await cartStore.addItem(product.value.id, quantity.value)
}

// 切换收藏
const handleToggleFavorite = async () => {
  if (!product.value) return
  await productsStore.toggleFavorite(product.value.id)
}

// 分享商品
const handleShare = async () => {
  if (!product.value) return

  try {
    // 检查是否支持Web Share API
    if (navigator.share) {
      await navigator.share({
        title: product.value.name,
        text: `${product.value.name} - ¥${product.value.price}`,
        url: `${window.location.origin}/products/${product.value.id}`
      })
    } else {
      // 降级到复制链接
      const shareUrl = `${window.location.origin}/products/${product.value.id}`
      await navigator.clipboard.writeText(shareUrl)
      toast.add({
        title: '链接已复制',
        description: '商品链接已复制到剪贴板',
        color: 'green'
      })
    }
  } catch (error) {
    console.error('分享失败:', error)
    // 如果分享被取消或失败，不显示错误提示
    if (error.name !== 'AbortError') {
      toast.add({
        title: '分享失败',
        description: '请稍后重试',
        color: 'red'
      })
    }
  }
}

// 关注商家
const followMerchant = async () => {
  if (!authStore.isLoggedIn) {
    return navigateTo('/login')
  }

  // TODO: 实现关注商家功能
  toast.add({
    title: '功能开发中',
    description: '关注功能正在开发中',
    color: 'yellow'
  })
}

// 提交评价
const submitReview = async () => {
  if (!authStore.isLoggedIn) {
    return navigateTo('/login')
  }

  if (!reviewForm.content.trim()) {
    toast.add({
      title: '请填写评价内容',
      color: 'yellow'
    })
    return
  }

  if (!product.value) return

  try {
    isSubmittingReview.value = true

    // 调用评价API
    await $fetch(`/api/products/${product.value.id}/reviews`, {
      method: 'POST',
      body: {
        rating: reviewForm.rating,
        content: reviewForm.content,
        anonymous: reviewForm.anonymous
      }
    })

    toast.add({
      title: '评价提交成功',
      color: 'green'
    })

    showReviewModal.value = false
    reviewForm.rating = 5
    reviewForm.content = ''
    reviewForm.anonymous = false

    // 重新获取商品信息以更新评价统计
    await fetchProduct()
  } catch (error: any) {
    console.error('提交评价失败:', error)
    toast.add({
      title: '提交失败',
      description: error.data?.message || error.message || '请稍后重试',
      color: 'red'
    })
  } finally {
    isSubmittingReview.value = false
  }
}

// 获取商品可用优惠券
const fetchProductCoupons = async () => {
  if (!product.value) return

  try {
    const response = await $fetch<any>(`/api/products/${product.value.id}/coupons`)
    const coupons = response.data.coupons
    availableCoupons.value = [...coupons.shared, ...coupons.exclusive]
  } catch (error) {
    console.error('获取优惠券失败:', error)
  }
}

// 领取优惠券
const claimCoupon = async (couponId: number) => {
  if (!authStore.isLoggedIn) {
    toast.add({
      title: '请先登录',
      description: '登录后即可领取优惠券',
      color: 'yellow'
    })
    return navigateTo('/login')
  }

  try {
    claimingCouponId.value = couponId

    await $fetch(`/api/coupons/${couponId}/claim`, {
      method: 'POST'
    })

    toast.add({
      title: '领取成功',
      description: '优惠券已添加到您的账户',
      color: 'green'
    })

    // 更新优惠券状态
    const coupon = availableCoupons.value.find(c => c.id === couponId)
    if (coupon) {
      coupon.isReceived = true
    }
  } catch (error: any) {
    console.error('领取优惠券失败:', error)
    toast.add({
      title: '领取失败',
      description: error.data?.message || '请稍后重试',
      color: 'red'
    })
  } finally {
    claimingCouponId.value = null
  }
}

// 格式化日期
const formatDate = (date: string | Date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

// 页面加载时获取商品详情
onMounted(async () => {
  try {
    product.value = await productsStore.fetchProduct(productId)
    if (product.value && product.value.images.length > 0) {
      currentImage.value = product.value.images[0]
    }
    // 获取优惠券
    await fetchProductCoupons()
  } catch (error) {
    // 商品不存在，跳转到404页面
    throw createError({
      statusCode: 404,
      statusMessage: '商品不存在'
    })
  }
})
</script>
