import { z } from 'zod'
import { defineApiHandler } from '~/utils/api'

/**
 * 提交商品评价
 * POST /api/products/:id/reviews
 */

// 请求数据验证schema
const reviewSchema = z.object({
  rating: z.number().min(1, '评分不能少于1星').max(5, '评分不能超过5星'),
  content: z.string().min(1, '评价内容不能为空').max(1000, '评价内容不能超过1000字'),
  images: z.array(z.string()).optional(),
  anonymous: z.boolean().optional().default(false)
})

export default defineApiHandler(async event => {
  // 只允许POST请求
  assertMethod(event, 'POST')

  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  // 获取商品ID
  const productId = parseInt(getRouterParam(event, 'id') || '0')
  if (!productId || isNaN(productId)) {
    throw new ValidationError('商品ID格式不正确')
  }

  // 获取请求体数据
  const body = await readBody(event)

  // 验证请求数据
  const validatedData = reviewSchema.parse(body)

  // 检查商品是否存在
  const product = await prisma.product.findUnique({
    where: { id: productId },
    select: { id: true, name: true, status: true }
  })

  if (!product) {
    throw new NotFoundError('商品不存在')
  }

  if (product.status !== 'ACTIVE') {
    throw new BusinessError('商品已下架，无法评价')
  }

  // 检查用户是否已购买过该商品
  const hasPurchased = await prisma.orderItem.findFirst({
    where: {
      productId,
      order: {
        userId,
        status: 'COMPLETED'
      }
    }
  })

  if (!hasPurchased) {
    throw new BusinessError('只有购买过的商品才能评价')
  }

  // 检查是否已经评价过
  const existingReview = await prisma.review.findFirst({
    where: {
      productId,
      userId
    }
  })

  if (existingReview) {
    throw new BusinessError('您已经评价过该商品')
  }

  // 创建评价
  const review = await prisma.review.create({
    data: {
      productId,
      userId,
      rating: validatedData.rating,
      content: validatedData.content,
      images: validatedData.images || [],
      anonymous: validatedData.anonymous,
      status: 'PUBLISHED'
    },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          nickname: true,
          avatar: true
        }
      }
    }
  })

  // 更新商品评分统计
  const reviewStats = await prisma.review.aggregate({
    where: {
      productId,
      status: 'PUBLISHED'
    },
    _avg: {
      rating: true
    },
    _count: {
      id: true
    }
  })

  await prisma.product.update({
    where: { id: productId },
    data: {
      rating: reviewStats._avg.rating || 0,
      reviewsCount: reviewStats._count.id
    }
  })

  // 格式化返回数据
  const formattedReview = {
    id: review.id,
    rating: review.rating,
    content: review.content,
    images: review.images,
    anonymous: review.anonymous,
    status: review.status,
    createdAt: review.createdAt,
    updatedAt: review.updatedAt,
    user: review.anonymous ? null : review.user
  }

  return createSuccessResponse(formattedReview, '评价提交成功')
})
