/**
 * 获取订单列表
 * GET /api/orders
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  const query = getQuery(event)

  // 解析分页参数
  const { page, pageSize, skip, take } = parsePaginationQuery(query)

  // 解析排序参数
  const allowedSortFields = ['createdAt', 'updatedAt', 'paymentAmount']
  const orderBy = parseSortQuery(query, allowedSortFields)

  // 构建查询条件
  const where: any = {
    userId
  }

  // 订单状态过滤
  if (query.status) {
    where.status = query.status
  }

  // 支付状态过滤
  if (query.paymentStatus) {
    where.paymentStatus = query.paymentStatus
  }

  // 日期范围过滤
  if (query.startDate || query.endDate) {
    where.createdAt = {}
    if (query.startDate) {
      where.createdAt.gte = new Date(query.startDate as string)
    }
    if (query.endDate) {
      where.createdAt.lte = new Date(query.endDate as string)
    }
  }

  // 查询订单列表和总数
  const [orders, total] = await Promise.all([
    prisma.order.findMany({
      where,
      orderBy,
      skip,
      take,
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                images: true,
                status: true
              }
            }
          }
        },
        shippingAddress: true,
        payments: {
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    }),
    prisma.order.count({ where })
  ])

  // 格式化订单数据
  const formattedOrders = orders.map(order => ({
    ...order,
    totalAmount: order.totalAmount.toNumber(),
    discountAmount: order.discountAmount.toNumber(),
    shippingAmount: order.shippingAmount.toNumber(),
    paymentAmount: order.paymentAmount.toNumber(),
    items: order.items.map(item => ({
      ...item,
      price: item.price.toNumber(),
      totalAmount: item.totalAmount.toNumber()
    })),
    latestPayment: order.payments[0]
      ? {
          ...order.payments[0],
          amount: order.payments[0].amount.toNumber()
        }
      : null,
    payments: undefined
  }))

  return createPaginatedResponse(formattedOrders, total, page, pageSize, '获取订单列表成功')
})
