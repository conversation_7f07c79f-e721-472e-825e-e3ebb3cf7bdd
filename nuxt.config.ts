// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },

  // CSS框架
  modules: ['@nuxt/ui', '@pinia/nuxt', '@vueuse/nuxt', '@nuxtjs/google-fonts'],

  // TypeScript配置
  typescript: {
    typeCheck: true
  },

  // CSS配置
  css: ['~/assets/css/main.css'],

  // Google字体
  googleFonts: {
    families: {
      Inter: [400, 500, 600, 700],
      'Noto+Sans+SC': [400, 500, 600, 700]
    }
  },

  // 运行时配置
  runtimeConfig: {
    // 服务端环境变量
    jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret-key',
    dbHost: process.env.DB_HOST || 'localhost',
    dbPort: process.env.DB_PORT || '5432',
    dbUser: process.env.DB_USER || 'postgres',
    dbPassword: process.env.DB_PASSWORD || '',
    dbName: process.env.DB_NAME || 'social_shop',
    databaseUrl: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/social_shop',
    redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',

    // 客户端环境变量
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || '/api',
      appName: '社交购物网站'
    }
  },

  // 服务端渲染配置
  ssr: true,

  // 路由配置 - 在Nuxt 3中，全局中间件应该放在middleware目录中并自动应用

  // 构建配置
  build: {
    transpile: ['@headlessui/vue']
  },

  // 开发服务器配置
  devServer: {
    port: 3000
  }
})
