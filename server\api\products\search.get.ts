/**
 * 商品搜索接口
 * GET /api/products/search
 */

import { logSearch } from '~/server/utils/logger'
import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  const query = getQuery(event)

  // 获取搜索关键词
  const keyword = query.q as string
  if (!keyword || keyword.trim().length === 0) {
    throw new ValidationError('搜索关键词不能为空')
  }

  // 解析分页参数
  const { page, pageSize, skip, take } = parsePaginationQuery(query)

  // 解析排序参数
  const allowedSortFields = ['createdAt', 'price', 'sales', 'rating']
  const orderBy = parseSortQuery(query, allowedSortFields)

  // 解析筛选参数
  const filters = {
    categoryId: query.categoryId ? parseInt(query.categoryId as string) : undefined,
    minPrice: query.minPrice ? parseFloat(query.minPrice as string) : undefined,
    maxPrice: query.maxPrice ? parseFloat(query.maxPrice as string) : undefined,
    merchantId: query.merchantId ? parseInt(query.merchantId as string) : undefined,
    inStock: query.inStock === 'true'
  }

  // 构建搜索条件
  const where: any = {
    status: 'ACTIVE',
    OR: [
      {
        name: {
          contains: keyword,
          mode: 'insensitive'
        }
      },
      {
        description: {
          contains: keyword,
          mode: 'insensitive'
        }
      },
      {
        tags: {
          hasSome: [keyword]
        }
      }
    ]
  }

  // 应用筛选条件
  if (filters.categoryId) {
    where.categoryId = filters.categoryId
  }

  if (filters.minPrice || filters.maxPrice) {
    where.price = {}
    if (filters.minPrice) {
      where.price.gte = filters.minPrice
    }
    if (filters.maxPrice) {
      where.price.lte = filters.maxPrice
    }
  }

  if (filters.merchantId) {
    where.merchantId = filters.merchantId
  }

  if (filters.inStock) {
    where.stock = { gt: 0 }
  }

  // 查询商品列表和总数
  const [products, total] = await Promise.all([
    prisma.product.findMany({
      where,
      orderBy,
      skip,
      take,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        merchant: {
          select: {
            id: true,
            username: true,
            nickname: true,
            avatar: true
          }
        },
        _count: {
          select: {
            reviews: true,
            favorites: true
          }
        }
      }
    }),
    prisma.product.count({ where })
  ])

  // 格式化商品数据
  const formattedProducts = products.map(product => ({
    id: product.id,
    name: product.name,
    description: product.description,
    price: product.price.toNumber(),
    originalPrice: product.originalPrice?.toNumber() || null,
    images: product.images,
    stock: product.stock,
    sales: product.sales,
    rating: product.rating?.toNumber() || 0,
    reviewsCount: product._count.reviews,
    favoritesCount: product._count.favorites,
    tags: product.tags,
    status: product.status,
    createdAt: product.createdAt,
    updatedAt: product.updatedAt,
    category: product.category,
    merchant: product.merchant
  }))

  // 计算总页数
  const totalPages = Math.ceil(total / pageSize)

  // 记录搜索日志
  await logSearch(keyword, total, filters, event, event.context.user?.id)

  const result = {
    items: formattedProducts,
    total,
    page,
    pageSize,
    totalPages,
    keyword,
    filters: {
      categoryId: filters.categoryId,
      minPrice: filters.minPrice,
      maxPrice: filters.maxPrice,
      merchantId: filters.merchantId,
      inStock: filters.inStock
    }
  }

  return createSuccessResponse(result, `找到 ${total} 个相关商品`)
})
