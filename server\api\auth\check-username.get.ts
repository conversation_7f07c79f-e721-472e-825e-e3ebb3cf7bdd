/**
 * 检查用户名是否可用
 * GET /api/auth/check-username
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  const query = getQuery(event)

  // 获取用户名参数
  const username = query.username as string
  if (!username || username.trim().length === 0) {
    throw new ValidationError('用户名不能为空')
  }

  // 验证用户名格式
  const usernameValidation = validateUsername(username.trim())
  if (!usernameValidation.isValid) {
    return createSuccessResponse(
      {
        available: false,
        reason: 'format',
        message: usernameValidation.errors[0] || '用户名格式不正确'
      },
      '用户名格式不正确'
    )
  }

  try {
    // 检查用户名是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { username: username.trim() },
      select: { id: true }
    })

    const isAvailable = !existingUser

    return createSuccessResponse(
      {
        available: isAvailable,
        reason: isAvailable ? null : 'exists',
        message: isAvailable ? '用户名可用' : '用户名已被使用'
      },
      isAvailable ? '用户名可用' : '用户名已被使用'
    )
  } catch (error) {
    console.error('检查用户名失败:', error)
    throw new InternalServerError('检查用户名失败')
  }
})

/**
 * 验证用户名格式
 */
function validateUsername(username: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  // 长度检查
  if (username.length < 3) {
    errors.push('用户名至少需要3个字符')
  } else if (username.length > 20) {
    errors.push('用户名最多20个字符')
  }

  // 字符检查：只允许字母、数字、下划线
  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    errors.push('用户名只能包含字母、数字和下划线')
  }

  // 不能以数字开头
  if (/^[0-9]/.test(username)) {
    errors.push('用户名不能以数字开头')
  }

  // 不能是保留词
  const reservedWords = [
    'admin',
    'administrator',
    'root',
    'system',
    'api',
    'www',
    'mail',
    'ftp',
    'test',
    'guest',
    'user',
    'support',
    'help',
    'service',
    'null',
    'undefined'
  ]

  if (reservedWords.includes(username.toLowerCase())) {
    errors.push('该用户名为系统保留词，不可使用')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
