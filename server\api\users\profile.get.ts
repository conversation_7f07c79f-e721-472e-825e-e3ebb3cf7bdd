/**
 * 获取用户个人信息
 * GET /api/users/profile
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  // 查询用户信息
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      username: true,
      email: true,
      phone: true,
      avatar: true,
      nickname: true,
      bio: true,
      role: true,
      status: true,
      createdAt: true,
      updatedAt: true,
      // 统计信息
      _count: {
        select: {
          posts: true,
          followers: true,
          following: true,
          products: true,
          orders: true
        }
      }
    }
  })

  if (!user) {
    throw new NotFoundError('用户不存在')
  }

  // 格式化返回数据
  const userProfile = {
    ...user,
    postsCount: user._count.posts,
    followersCount: user._count.followers,
    followingCount: user._count.following,
    productsCount: user._count.products,
    ordersCount: user._count.orders
  }

  // 移除_count字段
  delete (userProfile as any)._count

  return createSuccessResponse(userProfile, '获取用户信息成功')
})
