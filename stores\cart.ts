import type { CartItem } from '~/types'

/**
 * 购物车状态管理
 */
export const useCartStore = defineStore('cart', () => {
  // 状态
  const items = ref<CartItem[]>([])
  const isLoading = ref(false)
  const summary = ref({
    totalItems: 0,
    selectedCount: 0,
    totalAmount: 0,
    totalOriginalAmount: 0,
    discountAmount: 0
  })

  // API实例
  const request = createAuthenticatedRequest()
  const authStore = useAuthStore()
  const toast = useToast()

  // 获取购物车
  const fetchCart = async () => {
    if (!authStore.isLoggedIn) {
      clearCart()
      return
    }

    try {
      isLoading.value = true

      const response = await request.get<{
        items: CartItem[]
        summary: typeof summary.value
      }>('/api/cart')

      items.value = response.items
      summary.value = response.summary
    } catch (error: any) {
      console.error('获取购物车失败:', error)
      if (error.statusCode !== 401) {
        toast.add({
          title: '获取购物车失败',
          description: error.message || '请稍后重试',
          color: 'red'
        })
      }
    } finally {
      isLoading.value = false
    }
  }

  // 添加商品到购物车
  const addItem = async (productId: number, quantity: number = 1) => {
    if (!authStore.isLoggedIn) {
      toast.add({
        title: '请先登录',
        description: '登录后即可添加到购物车',
        color: 'yellow'
      })
      return navigateTo('/login')
    }

    try {
      const cartItem = await request.post<CartItem>('/api/cart', {
        productId,
        quantity
      })

      // 更新本地状态
      const existingIndex = items.value.findIndex(item => item.product.id === productId)
      if (existingIndex !== -1) {
        items.value[existingIndex] = cartItem
      } else {
        items.value.push(cartItem)
      }

      // 重新计算汇总
      calculateSummary()

      toast.add({
        title: '添加成功',
        description: '商品已添加到购物车',
        color: 'green'
      })
    } catch (error: any) {
      console.error('添加到购物车失败:', error)
      toast.add({
        title: '添加失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 更新商品数量
  const updateQuantity = async (itemId: number, quantity: number) => {
    if (!authStore.isLoggedIn) return

    if (quantity <= 0) {
      return removeItem(itemId)
    }

    try {
      const cartItem = await request.put<CartItem>(`/api/cart/${itemId}`, {
        quantity
      })

      // 更新本地状态
      const index = items.value.findIndex(item => item.id === itemId)
      if (index !== -1) {
        items.value[index] = cartItem
      }

      // 重新计算汇总
      calculateSummary()
    } catch (error: any) {
      console.error('更新数量失败:', error)
      toast.add({
        title: '更新失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 删除商品
  const removeItem = async (itemId: number) => {
    if (!authStore.isLoggedIn) return

    try {
      await request.delete(`/api/cart/${itemId}`)

      // 更新本地状态
      const index = items.value.findIndex(item => item.id === itemId)
      if (index !== -1) {
        items.value.splice(index, 1)
      }

      // 重新计算汇总
      calculateSummary()

      toast.add({
        title: '商品已移除',
        color: 'blue'
      })
    } catch (error: any) {
      console.error('删除商品失败:', error)
      toast.add({
        title: '删除失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 切换选中状态
  const toggleSelected = async (itemId: number, selected: boolean) => {
    if (!authStore.isLoggedIn) return

    try {
      const cartItem = await request.patch<CartItem>(`/api/cart/${itemId}`, {
        selected
      })

      // 更新本地状态
      const index = items.value.findIndex(item => item.id === itemId)
      if (index !== -1) {
        items.value[index] = cartItem
      }

      // 重新计算汇总
      calculateSummary()
    } catch (error: any) {
      console.error('切换选中状态失败:', error)
      toast.add({
        title: '操作失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 全选/取消全选
  const toggleSelectAll = async (selected: boolean) => {
    if (!authStore.isLoggedIn) return

    try {
      await request.patch('/api/cart/select-all', { selected })

      // 更新本地状态
      items.value.forEach(item => {
        item.selected = selected
      })

      // 重新计算汇总
      calculateSummary()
    } catch (error: any) {
      console.error('全选操作失败:', error)
      toast.add({
        title: '操作失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 清空购物车
  const clearCart = async () => {
    if (!authStore.isLoggedIn) {
      items.value = []
      summary.value = {
        totalItems: 0,
        selectedCount: 0,
        totalAmount: 0,
        totalOriginalAmount: 0,
        discountAmount: 0
      }
      return
    }

    try {
      await request.delete('/api/cart/clear')

      items.value = []
      summary.value = {
        totalItems: 0,
        selectedCount: 0,
        totalAmount: 0,
        totalOriginalAmount: 0,
        discountAmount: 0
      }

      toast.add({
        title: '购物车已清空',
        color: 'blue'
      })
    } catch (error: any) {
      console.error('清空购物车失败:', error)
      toast.add({
        title: '清空失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 批量删除选中商品
  const removeSelected = async () => {
    if (!authStore.isLoggedIn) return

    const selectedItems = items.value.filter(item => item.selected)
    if (selectedItems.length === 0) {
      toast.add({
        title: '请选择要删除的商品',
        color: 'yellow'
      })
      return
    }

    try {
      const itemIds = selectedItems.map(item => item.id)
      await request.delete('/api/cart/batch', { body: { itemIds } })

      // 更新本地状态
      items.value = items.value.filter(item => !item.selected)

      // 重新计算汇总
      calculateSummary()

      toast.add({
        title: `已删除 ${selectedItems.length} 件商品`,
        color: 'blue'
      })
    } catch (error: any) {
      console.error('批量删除失败:', error)
      toast.add({
        title: '删除失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 计算汇总信息
  const calculateSummary = () => {
    const totalItems = items.value.reduce((sum, item) => sum + item.quantity, 0)
    const selectedItems = items.value.filter(item => item.selected)
    const selectedCount = selectedItems.length

    const totalAmount = selectedItems.reduce((sum, item) => sum + item.product.price * item.quantity, 0)

    const totalOriginalAmount = selectedItems.reduce(
      (sum, item) => sum + (item.product.originalPrice || item.product.price) * item.quantity,
      0
    )

    const discountAmount = totalOriginalAmount - totalAmount

    summary.value = {
      totalItems,
      selectedCount,
      totalAmount,
      totalOriginalAmount,
      discountAmount
    }
  }

  // 获取选中的商品
  const getSelectedItems = () => items.value.filter(item => item.selected)

  // 检查商品是否在购物车中
  const isInCart = (productId: number) => items.value.some(item => item.product.id === productId)

  // 获取商品在购物车中的数量
  const getQuantity = (productId: number) => {
    const item = items.value.find(item => item.product.id === productId)
    return item ? item.quantity : 0
  }

  // 计算属性
  const isEmpty = computed(() => items.value.length === 0)
  const hasSelected = computed(() => summary.value.selectedCount > 0)
  const isAllSelected = computed(() => items.value.length > 0 && items.value.every(item => item.selected))

  // 监听登录状态变化
  watch(
    () => authStore.isLoggedIn,
    isLoggedIn => {
      if (isLoggedIn) {
        fetchCart()
      } else {
        clearCart()
      }
    },
    { immediate: true }
  )

  return {
    // 状态
    items: readonly(items),
    isLoading: readonly(isLoading),
    summary: readonly(summary),

    // 方法
    fetchCart,
    addItem,
    updateQuantity,
    removeItem,
    toggleSelected,
    toggleSelectAll,
    clearCart,
    removeSelected,
    getSelectedItems,

    // 工具方法
    isInCart,
    getQuantity,

    // 计算属性
    isEmpty,
    hasSelected,
    isAllSelected
  }
})
