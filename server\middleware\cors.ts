/**
 * CORS中间件
 * 处理跨域请求
 */

export default defineEventHandler(async event => {
  // 只处理API请求
  if (!event.node.req.url?.startsWith('/api/')) {
    return
  }

  // 设置CORS头
  setHeader(event, 'Access-Control-Allow-Origin', '*')
  setHeader(event, 'Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  setHeader(event, 'Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
  setHeader(event, 'Access-Control-Max-Age', 86400)

  // 处理预检请求
  if (event.node.req.method === 'OPTIONS') {
    event.node.res.statusCode = 204
    event.node.res.end()
    return
  }
})
