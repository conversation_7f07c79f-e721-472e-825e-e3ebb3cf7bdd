import { PrismaClient } from '@prisma/client'

/**
 * 数据库连接工具
 */

// 全局Prisma客户端实例
declare global {
  var __prisma: PrismaClient | undefined
}

// 创建Prisma客户端实例
export const prisma =
  globalThis.__prisma ||
  new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error']
  })

// 在开发环境中保存实例到全局变量，避免热重载时重复创建
if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma
}

/**
 * 数据库连接测试
 */
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$connect()
    await prisma.$queryRaw`SELECT 1`
    return true
  } catch (error) {
    console.error('数据库连接失败:', error)
    return false
  }
}

/**
 * 关闭数据库连接
 */
export async function closeDatabaseConnection(): Promise<void> {
  await prisma.$disconnect()
}

/**
 * 数据库事务执行
 */
export async function executeTransaction<T>(callback: Parameters<typeof prisma.$transaction>[0]): Promise<T> {
  return await prisma.$transaction(callback as any)
}

/**
 * 分页查询辅助函数
 */
export interface PaginationOptions {
  page?: number
  pageSize?: number
}

export interface PaginationResult<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export function calculatePagination(options: PaginationOptions) {
  const page = Math.max(1, options.page || 1)
  const pageSize = Math.min(100, Math.max(1, options.pageSize || 10))
  const skip = (page - 1) * pageSize

  return {
    page,
    pageSize,
    skip,
    take: pageSize
  }
}

export function formatPaginationResult<T>(
  items: T[],
  total: number,
  page: number,
  pageSize: number
): PaginationResult<T> {
  return {
    items,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  }
}

/**
 * 搜索查询辅助函数
 */
export function buildSearchCondition(keyword: string, fields: string[]) {
  if (!keyword || !fields.length) {
    return {}
  }

  return {
    OR: fields.map(field => ({
      [field]: {
        contains: keyword,
        mode: 'insensitive' as const
      }
    }))
  }
}

/**
 * 排序查询辅助函数
 */
export interface SortOptions {
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export function buildSortCondition(options: SortOptions, allowedFields: string[]) {
  const { sortBy = 'createdAt', sortOrder = 'desc' } = options

  // 验证排序字段是否允许
  if (!allowedFields.includes(sortBy)) {
    return { createdAt: 'desc' as const }
  }

  return {
    [sortBy]: sortOrder
  }
}

/**
 * 日期范围查询辅助函数
 */
export interface DateRangeOptions {
  startDate?: Date | string
  endDate?: Date | string
}

export function buildDateRangeCondition(options: DateRangeOptions, field: string = 'createdAt') {
  const conditions: any = {}

  if (options.startDate) {
    conditions.gte = new Date(options.startDate)
  }

  if (options.endDate) {
    conditions.lte = new Date(options.endDate)
  }

  return Object.keys(conditions).length > 0 ? { [field]: conditions } : {}
}

/**
 * 软删除辅助函数
 */
export function excludeDeleted() {
  return {
    deletedAt: null
  }
}

/**
 * 数据库健康检查
 */
export async function getDatabaseHealth() {
  try {
    const startTime = Date.now()
    await prisma.$queryRaw`SELECT 1`
    const responseTime = Date.now() - startTime

    // 获取数据库统计信息
    const userCount = await prisma.user.count()
    const productCount = await prisma.product.count()
    const orderCount = await prisma.order.count()

    return {
      status: 'healthy',
      responseTime,
      statistics: {
        users: userCount,
        products: productCount,
        orders: orderCount
      }
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * 清理过期数据
 */
export async function cleanupExpiredData() {
  try {
    // 清理过期的购物车项目（30天未更新）
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const deletedCartItems = await prisma.cartItem.deleteMany({
      where: {
        updatedAt: {
          lt: thirtyDaysAgo
        }
      }
    })

    // 清理已读的系统消息（7天前）
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    const deletedMessages = await prisma.message.deleteMany({
      where: {
        type: 'SYSTEM',
        isRead: true,
        createdAt: {
          lt: sevenDaysAgo
        }
      }
    })

    return {
      deletedCartItems: deletedCartItems.count,
      deletedMessages: deletedMessages.count
    }
  } catch (error) {
    console.error('清理过期数据失败:', error)
    throw error
  }
}
