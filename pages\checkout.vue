<template>
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">确认订单</h1>
        <p class="text-gray-600">请确认订单信息并选择收货地址</p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 左侧：订单信息 -->
        <div class="lg:col-span-2 space-y-6">
          <!-- 收货地址 -->
          <UCard>
            <template #header>
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">收货地址</h3>
                <UButton variant="ghost" size="sm" @click="showAddressModal = true">
                  <Icon name="heroicons:plus" class="w-4 h-4 mr-1" />
                  新增地址
                </UButton>
              </div>
            </template>

            <div v-if="ordersStore.shippingAddresses.length === 0" class="text-center py-8">
              <Icon name="heroicons:map-pin" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-500 mb-4">还没有收货地址</p>
              <UButton @click="showAddressModal = true">添加收货地址</UButton>
            </div>

            <div v-else class="space-y-3">
              <div
                v-for="address in ordersStore.shippingAddresses"
                :key="address.id"
                @click="selectedAddressId = address.id"
                class="flex items-start space-x-4 p-4 border rounded-lg cursor-pointer transition-colors"
                :class="{
                  'border-primary-500 bg-primary-50': selectedAddressId === address.id,
                  'border-gray-200 hover:bg-gray-50': selectedAddressId !== address.id
                }"
              >
                <URadio
                  :model-value="selectedAddressId"
                  :value="address.id"
                  @update:model-value="selectedAddressId = $event"
                />

                <div class="flex-1">
                  <div class="flex items-center space-x-2 mb-1">
                    <span class="font-medium">{{ address.name }}</span>
                    <span class="text-gray-600">{{ address.phone }}</span>
                    <UBadge v-if="address.isDefault" color="primary" variant="subtle" size="xs"> 默认 </UBadge>
                  </div>
                  <p class="text-gray-600 text-sm">
                    {{ address.province }} {{ address.city }} {{ address.district }} {{ address.address }}
                  </p>
                </div>

                <UDropdown :items="getAddressActions(address)">
                  <UButton variant="ghost" size="sm" @click.stop>
                    <Icon name="heroicons:ellipsis-horizontal" class="w-4 h-4" />
                  </UButton>
                </UDropdown>
              </div>
            </div>
          </UCard>

          <!-- 商品清单 -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">商品清单</h3>
            </template>

            <div class="space-y-4">
              <div
                v-for="item in checkoutItems"
                :key="item.id"
                class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg"
              >
                <img
                  :src="item.product.images[0] || '/images/placeholder.jpg'"
                  :alt="item.product.name"
                  class="w-16 h-16 object-cover rounded-lg"
                />

                <div class="flex-1">
                  <h4 class="font-medium text-gray-900">{{ item.product.name }}</h4>
                  <div class="flex items-center space-x-2 mt-1">
                    <span class="text-red-600 font-semibold">¥{{ item.product.price }}</span>
                    <span class="text-gray-500">×{{ item.quantity }}</span>
                  </div>
                </div>

                <div class="text-right">
                  <div class="font-semibold">¥{{ (item.product.price * item.quantity).toFixed(2) }}</div>
                </div>
              </div>
            </div>
          </UCard>

          <!-- 配送方式 -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">配送方式</h3>
            </template>

            <div class="space-y-3">
              <div
                v-for="method in shippingMethods"
                :key="method.id"
                @click="selectedShippingMethod = method.id"
                class="flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-colors"
                :class="{
                  'border-primary-500 bg-primary-50': selectedShippingMethod === method.id,
                  'border-gray-200 hover:bg-gray-50': selectedShippingMethod !== method.id
                }"
              >
                <div class="flex items-center space-x-3">
                  <URadio
                    :model-value="selectedShippingMethod"
                    :value="method.id"
                    @update:model-value="selectedShippingMethod = $event"
                  />
                  <div>
                    <div class="font-medium">{{ method.name }}</div>
                    <div class="text-sm text-gray-500">{{ method.description }}</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="font-semibold">{{ method.fee === 0 ? '免费' : `¥${method.fee}` }}</div>
                  <div class="text-xs text-gray-500">{{ method.estimatedDays }}天送达</div>
                </div>
              </div>
            </div>
          </UCard>

          <!-- 优惠券 -->
          <UCard>
            <template #header>
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">优惠券</h3>
                <UButton variant="ghost" size="sm" @click="showCouponModal = true">
                  选择优惠券
                  <Icon name="heroicons:chevron-right" class="w-4 h-4 ml-1" />
                </UButton>
              </div>
            </template>

            <div v-if="selectedCoupons.length === 0" class="text-center py-6">
              <Icon name="heroicons:ticket" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p class="text-gray-500 mb-3">暂未选择优惠券</p>
              <UButton variant="outline" @click="showCouponModal = true">选择优惠券</UButton>
            </div>

            <div v-else class="space-y-3">
              <div
                v-for="coupon in selectedCoupons"
                :key="coupon.id"
                class="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg"
              >
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <span
                      class="px-2 py-1 text-xs rounded"
                      :class="coupon.type === 'SHARED' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'"
                    >
                      {{ coupon.type === 'SHARED' ? '同享券' : '互斥券' }}
                    </span>
                    <span class="font-medium text-red-600">
                      {{ coupon.discountType === 'FIXED' ? `¥${coupon.discountValue}` : `${coupon.discountValue}%` }}
                    </span>
                  </div>
                  <p class="text-sm text-gray-600 mt-1">{{ coupon.name }}</p>
                  <p class="text-xs text-green-600">预计优惠 ¥{{ coupon.discountAmount.toFixed(2) }}</p>
                </div>

                <UButton variant="ghost" size="sm" @click="removeCoupon(coupon.id)">
                  <Icon name="heroicons:x-mark" class="w-4 h-4" />
                </UButton>
              </div>
            </div>
          </UCard>

          <!-- 订单备注 -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">订单备注</h3>
            </template>

            <UTextarea v-model="orderRemark" placeholder="如有特殊要求，请在此留言..." :rows="3" />
          </UCard>
        </div>

        <!-- 右侧：订单汇总 -->
        <div class="lg:col-span-1">
          <div class="sticky top-8">
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">订单汇总</h3>
              </template>

              <div class="space-y-4">
                <div class="flex justify-between">
                  <span class="text-gray-600">商品金额：</span>
                  <span>¥{{ orderSummary.subtotal.toFixed(2) }}</span>
                </div>

                <div class="flex justify-between">
                  <span class="text-gray-600">运费：</span>
                  <span>{{ orderSummary.shippingFee === 0 ? '免费' : `¥${orderSummary.shippingFee.toFixed(2)}` }}</span>
                </div>

                <div v-if="orderSummary.discount > 0" class="flex justify-between text-green-600">
                  <span>优惠金额：</span>
                  <span>-¥{{ orderSummary.discount.toFixed(2) }}</span>
                </div>

                <div class="border-t pt-4">
                  <div class="flex justify-between text-lg font-bold">
                    <span>应付金额：</span>
                    <span class="text-red-600">¥{{ orderSummary.total.toFixed(2) }}</span>
                  </div>
                </div>

                <UButton size="lg" block @click="handleSubmitOrder" :loading="isSubmitting" :disabled="!canSubmitOrder">
                  提交订单
                </UButton>

                <div class="text-xs text-gray-500 text-center">
                  点击"提交订单"表示您同意
                  <NuxtLink to="/terms" class="text-primary-600 hover:underline">《服务条款》</NuxtLink>
                </div>
              </div>
            </UCard>
          </div>
        </div>
      </div>
    </div>

    <!-- 地址管理模态框 -->
    <UModal v-model="showAddressModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">{{ editingAddress ? '编辑地址' : '新增地址' }}</h3>
        </template>

        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <UInput v-model="addressForm.name" placeholder="收货人姓名" label="收货人" />
            <UInput v-model="addressForm.phone" placeholder="手机号码" label="手机号" />
          </div>

          <div class="grid grid-cols-3 gap-4">
            <USelect v-model="addressForm.province" :options="provinces" placeholder="省份" label="省份" />
            <USelect v-model="addressForm.city" :options="cities" placeholder="城市" label="城市" />
            <USelect v-model="addressForm.district" :options="districts" placeholder="区县" label="区县" />
          </div>

          <UTextarea v-model="addressForm.address" placeholder="详细地址" label="详细地址" :rows="3" />

          <UCheckbox v-model="addressForm.isDefault" label="设为默认地址" />

          <div class="flex justify-end space-x-3">
            <UButton variant="ghost" @click="cancelAddressEdit"> 取消 </UButton>
            <UButton @click="saveAddress" :loading="isSavingAddress"> 保存 </UButton>
          </div>
        </div>
      </UCard>
    </UModal>

    <!-- 优惠券选择模态框 -->
    <UModal v-model="showCouponModal" :ui="{ width: 'w-full max-w-2xl' }">
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">选择优惠券</h3>
            <UButton variant="ghost" size="sm" @click="showCouponModal = false">
              <Icon name="heroicons:x-mark" class="w-5 h-5" />
            </UButton>
          </div>
        </template>

        <div v-if="isLoadingCoupons" class="text-center py-8">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          <p class="mt-2 text-gray-600">加载优惠券中...</p>
        </div>

        <div v-else-if="availableCoupons.length === 0" class="text-center py-8">
          <Icon name="heroicons:ticket" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-500">暂无可用优惠券</p>
        </div>

        <div v-else class="space-y-4 max-h-96 overflow-y-auto">
          <!-- 互斥券 -->
          <div v-if="availableCoupons.exclusive && availableCoupons.exclusive.length > 0">
            <h4 class="font-medium text-gray-900 mb-3">互斥券（只能选择一张）</h4>
            <div class="space-y-2">
              <div
                v-for="coupon in availableCoupons.exclusive"
                :key="coupon.id"
                @click="selectExclusiveCoupon(coupon)"
                class="flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-colors"
                :class="{
                  'border-purple-500 bg-purple-50': isSelectedCoupon(coupon.id),
                  'border-gray-200 hover:bg-gray-50': !isSelectedCoupon(coupon.id),
                  'opacity-50 cursor-not-allowed': !coupon.canUse
                }"
              >
                <div class="flex items-center space-x-3">
                  <URadio :model-value="getSelectedExclusiveCouponId()" :value="coupon.id" :disabled="!coupon.canUse" />
                  <div>
                    <div class="flex items-center space-x-2">
                      <span class="px-2 py-1 text-xs rounded bg-purple-100 text-purple-800">互斥券</span>
                      <span class="font-medium text-red-600">
                        {{ coupon.discountType === 'FIXED' ? `¥${coupon.discountValue}` : `${coupon.discountValue}%` }}
                      </span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">{{ coupon.name }}</p>
                    <p class="text-xs text-gray-500">
                      满{{ coupon.minOrderAmount }}元可用 · 预计优惠 ¥{{ coupon.discountAmount.toFixed(2) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 同享券 -->
          <div v-if="availableCoupons.shared && availableCoupons.shared.length > 0">
            <h4 class="font-medium text-gray-900 mb-3">同享券（可多选）</h4>
            <div class="space-y-2">
              <div
                v-for="coupon in availableCoupons.shared"
                :key="coupon.id"
                @click="toggleSharedCoupon(coupon)"
                class="flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-colors"
                :class="{
                  'border-blue-500 bg-blue-50': isSelectedCoupon(coupon.id),
                  'border-gray-200 hover:bg-gray-50': !isSelectedCoupon(coupon.id),
                  'opacity-50 cursor-not-allowed': !coupon.canUse
                }"
              >
                <div class="flex items-center space-x-3">
                  <UCheckbox :model-value="isSelectedCoupon(coupon.id)" :disabled="!coupon.canUse" />
                  <div>
                    <div class="flex items-center space-x-2">
                      <span class="px-2 py-1 text-xs rounded bg-blue-100 text-blue-800">同享券</span>
                      <span class="font-medium text-red-600">
                        {{ coupon.discountType === 'FIXED' ? `¥${coupon.discountValue}` : `${coupon.discountValue}%` }}
                      </span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">{{ coupon.name }}</p>
                    <p class="text-xs text-gray-500">
                      满{{ coupon.minOrderAmount }}元可用 · 预计优惠 ¥{{ coupon.discountAmount.toFixed(2) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-600">
              已选择 {{ selectedCoupons.length }} 张优惠券，预计优惠 ¥{{ totalCouponDiscount.toFixed(2) }}
            </div>
            <div class="space-x-2">
              <UButton variant="outline" @click="showCouponModal = false">取消</UButton>
              <UButton @click="confirmCouponSelection">确认选择</UButton>
            </div>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import type { CartItem, ShippingAddress } from '~/types'

// 页面SEO
useHead({
  title: '确认订单 - 社交购物网站'
})

// 状态管理
const cartStore = useCartStore()
const ordersStore = useOrdersStore()
const toast = useToast()

// 响应式数据
const checkoutItems = ref<CartItem[]>([])
const selectedAddressId = ref<number | undefined>(undefined)
const selectedShippingMethod = ref('standard')
const orderRemark = ref('')
const isSubmitting = ref(false)
const showAddressModal = ref(false)
const editingAddress = ref<ShippingAddress | null>(null)
const isSavingAddress = ref(false)

// 优惠券相关状态
const showCouponModal = ref(false)
const availableCoupons = ref<any>({ shared: [], exclusive: [] })
const selectedCoupons = ref<any[]>([])
const isLoadingCoupons = ref(false)

// 地址表单
const addressForm = reactive({
  name: '',
  phone: '',
  province: '',
  city: '',
  district: '',
  address: '',
  isDefault: false
})

// 配送方式
const shippingMethods = [
  {
    id: 'standard',
    name: '标准快递',
    description: '由第三方快递公司配送',
    fee: 0,
    estimatedDays: 3
  },
  {
    id: 'express',
    name: '特快专递',
    description: '更快的配送服务',
    fee: 15,
    estimatedDays: 1
  }
]

// 地区数据（简化版）
const provinces = ['北京市', '上海市', '广东省', '浙江省', '江苏省']
const cities = ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市']
const districts = ['朝阳区', '海淀区', '浦东新区', '黄浦区', '天河区', '福田区']

// 计算属性
const orderSummary = computed(() => {
  const subtotal = checkoutItems.value.reduce((sum, item) => sum + item.product.price * item.quantity, 0)

  const shippingMethod = shippingMethods.find(m => m.id === selectedShippingMethod.value)
  const shippingFee = shippingMethod?.fee || 0

  const discount = totalCouponDiscount.value
  const total = Math.max(0, subtotal + shippingFee - discount)

  return {
    subtotal,
    shippingFee,
    discount,
    total
  }
})

const canSubmitOrder = computed(
  () => checkoutItems.value.length > 0 && selectedAddressId.value && selectedShippingMethod.value
)

// 获取地址操作菜单
const getAddressActions = (address: ShippingAddress) => {
  const actions = [
    {
      label: '编辑',
      icon: 'heroicons:pencil',
      click: () => editAddress(address)
    }
  ]

  if (!address.isDefault) {
    actions.push({
      label: '设为默认',
      icon: 'heroicons:star',
      click: () => ordersStore.setDefaultAddress(address.id)
    })
  }

  actions.push({
    label: '删除',
    icon: 'heroicons:trash',
    click: () => deleteAddress(address.id)
  })

  return [actions]
}

// 编辑地址
const editAddress = (address: ShippingAddress) => {
  editingAddress.value = address
  Object.assign(addressForm, address)
  showAddressModal.value = true
}

// 删除地址
const deleteAddress = async (addressId: number) => {
  if (confirm('确定要删除这个地址吗？')) {
    await ordersStore.deleteShippingAddress(addressId)
  }
}

// 取消地址编辑
const cancelAddressEdit = () => {
  showAddressModal.value = false
  editingAddress.value = null
  Object.assign(addressForm, {
    name: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    address: '',
    isDefault: false
  })
}

// 保存地址
const saveAddress = async () => {
  try {
    isSavingAddress.value = true

    if (editingAddress.value) {
      await ordersStore.updateShippingAddress(editingAddress.value.id, addressForm)
    } else {
      const newAddress = await ordersStore.addShippingAddress(addressForm)
      if (!selectedAddressId.value) {
        selectedAddressId.value = newAddress.id
      }
    }

    cancelAddressEdit()
  } catch (error) {
    console.error('保存地址失败:', error)
  } finally {
    isSavingAddress.value = false
  }
}

// 提交订单
const handleSubmitOrder = async () => {
  if (!canSubmitOrder.value) return

  try {
    isSubmitting.value = true

    const orderData = {
      items: checkoutItems.value.map(item => ({
        productId: item.product.id,
        quantity: item.quantity
      })),
      shippingAddressId: selectedAddressId.value!,
      remark: orderRemark.value
    }

    const order = await ordersStore.createOrder(orderData)

    // 清空购物车中的已选商品
    const selectedItemIds = checkoutItems.value.map(item => item.id)
    for (const itemId of selectedItemIds) {
      await cartStore.removeItem(itemId)
    }

    // 跳转到支付页面
    await navigateTo(`/payment/${order.id}`)
  } catch (error) {
    console.error('提交订单失败:', error)
  } finally {
    isSubmitting.value = false
  }
}

// 优惠券相关方法
const fetchAvailableCoupons = async () => {
  try {
    isLoadingCoupons.value = true

    const orderAmount = orderSummary.value.subtotal
    const productIds = checkoutItems.value.map(item => item.product.id)
    const categoryIds = [...new Set(checkoutItems.value.map(item => item.product.categoryId))]
    const merchantIds = [...new Set(checkoutItems.value.map(item => item.product.merchantId))]

    const response = await $fetch<any>('/api/users/coupons', {
      params: {
        status: 'UNUSED',
        orderAmount,
        productIds: productIds.join(','),
        categoryIds: categoryIds.join(','),
        merchantIds: merchantIds.join(',')
      }
    })

    availableCoupons.value = response.data.coupons
  } catch (error) {
    console.error('获取优惠券失败:', error)
  } finally {
    isLoadingCoupons.value = false
  }
}

const isSelectedCoupon = (couponId: number) => {
  return selectedCoupons.value.some(c => c.id === couponId)
}

const getSelectedExclusiveCouponId = () => {
  const exclusiveCoupon = selectedCoupons.value.find(c => c.type === 'EXCLUSIVE')
  return exclusiveCoupon ? exclusiveCoupon.id : null
}

const selectExclusiveCoupon = (coupon: any) => {
  if (!coupon.canUse) return

  // 移除所有优惠券（互斥券不能与其他券同时使用）
  selectedCoupons.value = []

  // 添加选中的互斥券
  selectedCoupons.value.push(coupon)
}

const toggleSharedCoupon = (coupon: any) => {
  if (!coupon.canUse) return

  // 如果已选择互斥券，不能选择同享券
  const hasExclusiveCoupon = selectedCoupons.value.some(c => c.type === 'EXCLUSIVE')
  if (hasExclusiveCoupon) {
    toast.add({
      title: '不能同时使用',
      description: '选择互斥券时不能同时选择同享券',
      color: 'yellow'
    })
    return
  }

  const index = selectedCoupons.value.findIndex(c => c.id === coupon.id)
  if (index > -1) {
    selectedCoupons.value.splice(index, 1)
  } else {
    selectedCoupons.value.push(coupon)
  }
}

const removeCoupon = (couponId: number) => {
  const index = selectedCoupons.value.findIndex(c => c.id === couponId)
  if (index > -1) {
    selectedCoupons.value.splice(index, 1)
  }
}

const confirmCouponSelection = async () => {
  if (selectedCoupons.value.length === 0) {
    showCouponModal.value = false
    return
  }

  try {
    // 计算优惠金额
    const response = await $fetch<any>('/api/coupons/calculate', {
      method: 'POST',
      body: {
        userCouponIds: selectedCoupons.value.map(c => c.id),
        orderAmount: orderSummary.value.subtotal,
        items: checkoutItems.value.map(item => ({
          productId: item.product.id,
          quantity: item.quantity,
          price: item.product.price
        }))
      }
    })

    // 更新选中的优惠券信息
    selectedCoupons.value = response.data.coupons.filter((c: any) => c.applicable)

    showCouponModal.value = false

    toast.add({
      title: '优惠券选择成功',
      description: `预计优惠 ¥${response.data.totalDiscount.toFixed(2)}`,
      color: 'green'
    })
  } catch (error: any) {
    console.error('计算优惠券失败:', error)
    toast.add({
      title: '计算失败',
      description: error.data?.message || '请稍后重试',
      color: 'red'
    })
  }
}

// 计算属性
const totalCouponDiscount = computed(() => {
  return selectedCoupons.value.reduce((total, coupon) => total + (coupon.discountAmount || 0), 0)
})

// 页面加载时初始化数据
onMounted(async () => {
  // 获取购物车中选中的商品
  checkoutItems.value = cartStore.getSelectedItems()

  if (checkoutItems.value.length === 0) {
    toast.add({
      title: '没有选中的商品',
      description: '请先在购物车中选择要购买的商品',
      color: 'yellow'
    })
    navigateTo('/cart')
    return
  }

  // 获取收货地址
  await ordersStore.fetchShippingAddresses()

  // 设置默认地址
  if (ordersStore.defaultAddress) {
    selectedAddressId.value = ordersStore.defaultAddress.id
  }

  // 获取可用优惠券
  await fetchAvailableCoupons()
})
</script>
