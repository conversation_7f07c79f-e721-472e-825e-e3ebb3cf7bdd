/**
 * 头像上传接口
 * POST /api/upload/avatar
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  try {
    // 检查用户是否已登录
    const userId = event.context.user?.id
    if (!userId) {
      throw new AuthenticationError('请先登录')
    }

    // 解析表单数据
    const form = await readMultipartFormData(event)
    if (!form || form.length === 0) {
      throw new ValidationError('请选择要上传的头像')
    }

    // 只允许上传一个头像文件
    const avatarFile = form[0]
    if (!avatarFile.filename || !avatarFile.data) {
      throw new ValidationError('无效的头像文件')
    }

    // 使用头像上传配置
    const uploadedFile = await uploadFile(avatarFile, defaultUploadConfigs.avatars)

    // 更新用户头像
    await prisma.user.update({
      where: { id: userId },
      data: { avatar: uploadedFile }
    })

    return createSuccessResponse(
      {
        avatar: uploadedFile
      },
      '头像上传成功'
    )
  } catch (error) {
    console.error('头像上传失败:', error)

    if (error instanceof ApiError) {
      throw error
    }

    throw new InternalServerError('头像上传失败')
  }
})
