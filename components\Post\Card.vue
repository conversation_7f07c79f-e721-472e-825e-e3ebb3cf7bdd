<template>
  <UCard class="p-6">
    <!-- 用户信息 -->
    <div class="flex items-start justify-between mb-4">
      <div class="flex items-center space-x-3">
        <UserAvatar
          :src="post.user.avatar"
          :alt="post.user.nickname || post.user.username"
          size="md"
          :role="post.user.role"
          show-role-badge
        />
        <div>
          <div class="flex items-center space-x-2">
            <NuxtLink :to="`/users/${post.user.id}`" class="font-medium text-gray-900 hover:text-primary-600">
              {{ post.user.nickname || post.user.username }}
            </NuxtLink>
            <UButton v-if="!isOwnPost && authStore.isLoggedIn" variant="ghost" size="xs" @click="handleFollow">
              {{ isFollowing ? '已关注' : '关注' }}
            </UButton>
          </div>
          <div class="text-sm text-gray-500">{{ formatTime(post.createdAt) }}</div>
        </div>
      </div>

      <!-- 更多操作 -->
      <UDropdown :items="moreActions">
        <UButton variant="ghost" size="sm">
          <Icon name="heroicons:ellipsis-horizontal" class="w-4 h-4" />
        </UButton>
      </UDropdown>
    </div>

    <!-- 动态内容 -->
    <div class="mb-4">
      <p class="text-gray-900 whitespace-pre-wrap">{{ post.content }}</p>
    </div>

    <!-- 图片内容 -->
    <div v-if="post.images && post.images.length > 0" class="mb-4">
      <div class="grid gap-2" :class="getImageGridClass(post.images.length)">
        <img
          v-for="(image, index) in post.images"
          :key="index"
          :src="image"
          :alt="`动态图片 ${index + 1}`"
          class="w-full h-full object-cover rounded-lg cursor-pointer"
          :class="getImageClass(post.images.length, index)"
          @click="previewImage(image, index)"
        />
      </div>
    </div>

    <!-- 商品分享 -->
    <div v-if="post.product" class="mb-4">
      <NuxtLink
        :to="`/products/${post.product.id}`"
        class="block border rounded-lg p-4 hover:bg-gray-50 transition-colors"
      >
        <div class="flex items-center space-x-4">
          <img
            :src="post.product.images[0] || '/images/placeholder.jpg'"
            :alt="post.product.name"
            class="w-16 h-16 object-cover rounded-lg"
          />
          <div class="flex-1">
            <h4 class="font-medium text-gray-900 line-clamp-2">{{ post.product.name }}</h4>
            <div class="flex items-center space-x-2 mt-1">
              <span class="text-lg font-bold text-red-600">¥{{ post.product.price }}</span>
              <span
                v-if="post.product.originalPrice && post.product.originalPrice > post.product.price"
                class="text-sm text-gray-500 line-through"
              >
                ¥{{ post.product.originalPrice }}
              </span>
            </div>
          </div>
          <Icon name="heroicons:chevron-right" class="w-5 h-5 text-gray-400" />
        </div>
      </NuxtLink>
    </div>

    <!-- 互动统计 -->
    <div class="flex items-center justify-between text-sm text-gray-500 mb-4 pb-4 border-b">
      <div class="flex items-center space-x-4">
        <span v-if="post.likesCount > 0">{{ post.likesCount }}个赞</span>
        <span v-if="post.commentsCount > 0">{{ post.commentsCount }}条评论</span>
      </div>
      <div v-if="post.createdAt !== post.updatedAt" class="text-xs">已编辑</div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-6">
        <!-- 点赞 -->
        <button
          @click="$emit('like', post.id)"
          class="flex items-center space-x-1 text-gray-500 hover:text-red-500 transition-colors"
          :class="{ 'text-red-500': post.isLiked }"
        >
          <Icon :name="post.isLiked ? 'heroicons:heart-solid' : 'heroicons:heart'" class="w-5 h-5" />
          <span class="text-sm">{{ post.isLiked ? '已赞' : '点赞' }}</span>
        </button>

        <!-- 评论 -->
        <button
          @click="$emit('comment', post)"
          class="flex items-center space-x-1 text-gray-500 hover:text-blue-500 transition-colors"
        >
          <Icon name="heroicons:chat-bubble-left" class="w-5 h-5" />
          <span class="text-sm">评论</span>
        </button>

        <!-- 分享 -->
        <button
          @click="$emit('share', post)"
          class="flex items-center space-x-1 text-gray-500 hover:text-green-500 transition-colors"
        >
          <Icon name="heroicons:share" class="w-5 h-5" />
          <span class="text-sm">分享</span>
        </button>
      </div>
    </div>

    <!-- 评论列表 -->
    <div v-if="post.comments && post.comments.length > 0" class="mt-4 pt-4 border-t">
      <div class="space-y-3">
        <div v-for="comment in post.comments.slice(0, 3)" :key="comment.id" class="flex items-start space-x-3">
          <UserAvatar :src="comment.user.avatar" :alt="comment.user.nickname || comment.user.username" size="sm" />
          <div class="flex-1">
            <div class="bg-gray-50 rounded-lg p-3">
              <div class="flex items-center space-x-2 mb-1">
                <span class="font-medium text-sm">{{ comment.user.nickname || comment.user.username }}</span>
                <span class="text-xs text-gray-500">{{ formatTime(comment.createdAt) }}</span>
              </div>
              <p class="text-sm text-gray-900">{{ comment.content }}</p>
            </div>
          </div>
        </div>

        <div v-if="post.commentsCount > 3" class="text-center">
          <UButton variant="ghost" size="sm" @click="viewAllComments"> 查看全部{{ post.commentsCount }}条评论 </UButton>
        </div>
      </div>
    </div>

    <!-- 图片预览模态框 -->
    <UModal v-model="showImagePreview" :ui="{ width: 'w-full max-w-4xl' }">
      <div class="relative bg-black">
        <!-- 关闭按钮 -->
        <UButton
          variant="ghost"
          size="sm"
          class="absolute top-4 right-4 z-10 text-white hover:bg-white/20"
          @click="closePreview"
        >
          <Icon name="heroicons:x-mark" class="w-6 h-6" />
        </UButton>

        <!-- 图片容器 -->
        <div class="relative flex items-center justify-center min-h-[60vh] max-h-[80vh]">
          <img
            v-if="previewImages[currentImageIndex]"
            :src="previewImages[currentImageIndex]"
            :alt="`预览图片 ${currentImageIndex + 1}`"
            class="max-w-full max-h-full object-contain"
          />

          <!-- 左右切换按钮 -->
          <template v-if="previewImages.length > 1">
            <UButton
              variant="ghost"
              size="sm"
              class="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
              @click="switchPreviewImage('prev')"
            >
              <Icon name="heroicons:chevron-left" class="w-8 h-8" />
            </UButton>
            <UButton
              variant="ghost"
              size="sm"
              class="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
              @click="switchPreviewImage('next')"
            >
              <Icon name="heroicons:chevron-right" class="w-8 h-8" />
            </UButton>
          </template>
        </div>

        <!-- 图片指示器 -->
        <div v-if="previewImages.length > 1" class="absolute bottom-4 left-1/2 -translate-x-1/2">
          <div class="flex space-x-2">
            <button
              v-for="(_, index) in previewImages"
              :key="index"
              class="w-2 h-2 rounded-full transition-colors"
              :class="index === currentImageIndex ? 'bg-white' : 'bg-white/50'"
              @click="currentImageIndex = index"
            />
          </div>
        </div>

        <!-- 图片信息 -->
        <div class="absolute bottom-4 right-4 text-white text-sm">
          {{ currentImageIndex + 1 }} / {{ previewImages.length }}
        </div>
      </div>
    </UModal>
  </UCard>
</template>

<script setup lang="ts">
import type { Post } from '~/types'

// 组件属性
interface Props {
  post: Post
}

// 组件事件
interface Emits {
  like: [postId: number]
  comment: [post: Post]
  share: [post: Post]
  delete: [post: Post]
  report: [post: Post]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 状态管理
const authStore = useAuthStore()
const socialStore = useSocialStore()

// 计算属性
const isOwnPost = computed(() => authStore.user?.id === props.post.user.id)

const isFollowing = computed(() => socialStore.isFollowing(props.post.user.id))

const moreActions = computed(() => {
  const actions = []

  if (isOwnPost.value) {
    actions.push({
      label: '删除',
      icon: 'heroicons:trash',
      click: () => emit('delete', props.post)
    })
  } else {
    actions.push({
      label: '举报',
      icon: 'heroicons:flag',
      click: () => emit('report', props.post)
    })
  }

  return [actions]
})

// 获取图片网格样式
const getImageGridClass = (count: number) => {
  if (count === 1) return 'grid-cols-1'
  if (count === 2) return 'grid-cols-2'
  if (count === 3) return 'grid-cols-3'
  if (count === 4) return 'grid-cols-2'
  return 'grid-cols-3'
}

// 获取图片样式
const getImageClass = (count: number, index: number) => {
  if (count === 1) return 'aspect-video max-h-96'
  if (count === 2) return 'aspect-square'
  if (count === 3) return 'aspect-square'
  if (count === 4) return 'aspect-square'
  if (count > 4 && index === 4) return 'aspect-square relative'
  return 'aspect-square'
}

// 格式化时间
const formatTime = (date: string | Date) => {
  const now = new Date()
  const postDate = new Date(date)
  const diff = now.getTime() - postDate.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`

  return postDate.toLocaleDateString('zh-CN')
}

// 处理关注
const handleFollow = async () => {
  await socialStore.followUser(props.post.user.id)
}

// 图片预览相关状态
const showImagePreview = ref(false)
const previewImages = ref<readonly string[]>([])
const currentImageIndex = ref(0)

// 预览图片
const previewImage = (image: string, index: number) => {
  previewImages.value = props.post.images || []
  currentImageIndex.value = index
  showImagePreview.value = true
}

// 切换预览图片
const switchPreviewImage = (direction: 'prev' | 'next') => {
  if (direction === 'prev') {
    currentImageIndex.value = currentImageIndex.value > 0 ? currentImageIndex.value - 1 : previewImages.value.length - 1
  } else {
    currentImageIndex.value = currentImageIndex.value < previewImages.value.length - 1 ? currentImageIndex.value + 1 : 0
  }
}

// 关闭预览
const closePreview = () => {
  showImagePreview.value = false
}

// 查看全部评论
const viewAllComments = () => {
  navigateTo(`/social/posts/${props.post.id}`)
}
</script>

<style scoped>
.line-clamp-2 {
  display: box;
  line-clamp: 2;
  box-orient: vertical;
  overflow: hidden;
}
</style>
