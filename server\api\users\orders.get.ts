/**
 * 获取用户订单列表
 * GET /api/users/orders
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  const query = getQuery(event)

  // 解析分页参数
  const { page, pageSize, skip, take } = parsePaginationQuery(query)

  // 解析排序参数
  const allowedSortFields = ['createdAt', 'totalAmount', 'status']
  const orderBy = parseSortQuery(query, allowedSortFields)

  // 构建查询条件
  const where: any = {
    userId
  }

  // 状态过滤
  if (query.status && query.status !== 'ALL') {
    where.status = query.status as string
  }

  // 时间范围过滤
  if (query.startDate || query.endDate) {
    where.createdAt = {}
    if (query.startDate) {
      where.createdAt.gte = new Date(query.startDate as string)
    }
    if (query.endDate) {
      where.createdAt.lte = new Date(query.endDate as string)
    }
  }

  try {
    // 查询订单列表和总数
    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        orderBy,
        skip,
        take,
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  images: true,
                  price: true
                }
              }
            }
          },
          shippingAddress: {
            select: {
              recipientName: true,
              phone: true,
              province: true,
              city: true,
              district: true,
              address: true
            }
          },
          coupons: {
            include: {
              coupon: {
                select: {
                  id: true,
                  name: true,
                  discountType: true,
                  discountValue: true
                }
              }
            }
          }
        }
      }),
      prisma.order.count({ where })
    ])

    // 格式化订单数据
    const formattedOrders = orders.map(order => ({
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
      totalAmount: order.totalAmount.toNumber(),
      subtotalAmount: order.subtotalAmount.toNumber(),
      shippingFee: order.shippingFee.toNumber(),
      discountAmount: order.discountAmount.toNumber(),
      paymentMethod: order.paymentMethod,
      shippingMethod: order.shippingMethod,
      remark: order.remark,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      paidAt: order.paidAt,
      shippedAt: order.shippedAt,
      completedAt: order.completedAt,
      items: order.items.map(item => ({
        id: item.id,
        quantity: item.quantity,
        price: item.price.toNumber(),
        product: item.product
      })),
      shippingAddress: order.shippingAddress,
      coupons: order.coupons.map(orderCoupon => ({
        id: orderCoupon.id,
        discountAmount: orderCoupon.discountAmount.toNumber(),
        coupon: {
          id: orderCoupon.coupon.id,
          name: orderCoupon.coupon.name,
          discountType: orderCoupon.coupon.discountType,
          discountValue: orderCoupon.coupon.discountValue.toNumber()
        }
      }))
    }))

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize)

    const result = {
      items: formattedOrders,
      total,
      page,
      pageSize,
      totalPages,
      summary: {
        totalOrders: total,
        pendingOrders: await prisma.order.count({ where: { ...where, status: 'PENDING' } }),
        completedOrders: await prisma.order.count({ where: { ...where, status: 'DELIVERED' } }),
        totalAmount: formattedOrders.reduce((sum, order) => sum + order.totalAmount, 0)
      }
    }

    return createSuccessResponse(result, '获取订单列表成功')
  } catch (error) {
    console.error('获取用户订单失败:', error)
    throw new InternalServerError('获取订单列表失败')
  }
})
