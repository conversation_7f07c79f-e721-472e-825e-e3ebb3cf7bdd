/**
 * 获取用户操作日志列表
 * GET /api/admin/logs/users
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  // 检查管理员权限
  const user = event.context.user
  if (!user || user.role !== 'admin') {
    throw new AuthorizationError('需要管理员权限')
  }

  const query = getQuery(event)

  // 解析分页参数
  const { page, pageSize, skip, take } = parsePaginationQuery(query)

  // 解析排序参数
  const allowedSortFields = ['createdAt', 'action', 'userId']
  const orderBy = parseSortQuery(query, allowedSortFields, 'createdAt', 'desc')

  // 构建查询条件
  const where: any = {}

  // 用户ID过滤
  if (query.userId) {
    where.userId = parseInt(query.userId as string)
  }

  // 操作类型过滤
  if (query.action) {
    where.action = query.action as string
  }

  // 资源类型过滤
  if (query.resource) {
    where.resource = query.resource as string
  }

  // 资源ID过滤
  if (query.resourceId) {
    where.resourceId = parseInt(query.resourceId as string)
  }

  // 时间范围过滤
  if (query.startDate || query.endDate) {
    where.createdAt = {}
    if (query.startDate) {
      where.createdAt.gte = new Date(query.startDate as string)
    }
    if (query.endDate) {
      where.createdAt.lte = new Date(query.endDate as string)
    }
  }

  // IP地址过滤
  if (query.ipAddress) {
    where.ipAddress = query.ipAddress as string
  }

  try {
    // 查询用户日志和总数
    const [userLogs, total] = await Promise.all([
      prisma.userLog.findMany({
        where,
        orderBy,
        skip,
        take,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              nickname: true,
              email: true,
              role: true
            }
          }
        }
      }),
      prisma.userLog.count({ where })
    ])

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize)

    // 获取统计信息
    const actionStats = await prisma.userLog.groupBy({
      by: ['action'],
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
        }
      },
      _count: {
        action: true
      },
      orderBy: {
        _count: {
          action: 'desc'
        }
      }
    })

    // 获取活跃用户统计
    const activeUsers = await prisma.userLog.groupBy({
      by: ['userId'],
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
        },
        userId: {
          not: null
        }
      },
      _count: {
        userId: true
      },
      orderBy: {
        _count: {
          userId: 'desc'
        }
      },
      take: 10
    })

    const result = {
      items: userLogs,
      total,
      page,
      pageSize,
      totalPages,
      stats: {
        actionStats: actionStats.map(item => ({
          action: item.action,
          count: item._count.action
        })),
        activeUsersCount: activeUsers.length,
        topActiveUsers: activeUsers.map(item => ({
          userId: item.userId,
          actionCount: item._count.userId
        }))
      }
    }

    return createSuccessResponse(result, '获取用户日志成功')
  } catch (error) {
    console.error('获取用户日志失败:', error)
    throw new InternalServerError('获取用户日志失败')
  }
})
