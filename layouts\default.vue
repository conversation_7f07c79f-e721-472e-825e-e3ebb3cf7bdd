<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b">
      <nav class="container mx-auto px-4">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <div class="flex items-center">
            <NuxtLink to="/" class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">S</span>
              </div>
              <span class="text-xl font-bold text-gray-900">社交购物</span>
            </NuxtLink>
          </div>

          <!-- 搜索框 -->
          <div class="flex-1 max-w-lg mx-8">
            <div class="relative">
              <UInput
                v-model="searchQuery"
                placeholder="搜索商品、用户、话题..."
                size="md"
                class="w-full"
                @keyup.enter="handleSearch"
              >
                <template #trailing>
                  <UButton variant="ghost" size="sm" @click="handleSearch" :disabled="!searchQuery">
                    <Icon name="heroicons:magnifying-glass" />
                  </UButton>
                </template>
              </UInput>
            </div>
          </div>

          <!-- 导航菜单 -->
          <div class="flex items-center space-x-4">
            <!-- 购物车 -->
            <UButton variant="ghost" size="sm" @click="navigateTo('/cart')">
              <div class="relative">
                <Icon name="heroicons:shopping-cart" class="w-5 h-5" />
                <UBadge
                  v-if="cartStore.summary.totalItems > 0"
                  color="red"
                  variant="solid"
                  size="xs"
                  class="absolute -top-2 -right-2"
                >
                  {{ cartStore.summary.totalItems }}
                </UBadge>
              </div>
            </UButton>

            <!-- 通知 -->
            <UButton variant="ghost" size="sm" @click="navigateTo('/notifications')">
              <div class="relative">
                <Icon name="heroicons:bell" class="w-5 h-5" />
                <UBadge
                  v-if="notificationsStore.unreadCount > 0"
                  color="red"
                  variant="solid"
                  size="xs"
                  class="absolute -top-2 -right-2"
                >
                  {{ notificationsStore.unreadCount }}
                </UBadge>
              </div>
            </UButton>

            <!-- 用户菜单 -->
            <UDropdown v-if="authStore.isLoggedIn" :items="userMenuItems">
              <UButton variant="ghost" size="sm">
                <img
                  :src="authStore.user?.avatar || '/images/default-avatar.jpg'"
                  :alt="authStore.user?.nickname || authStore.user?.username"
                  class="w-6 h-6 rounded-full"
                />
                <span class="ml-2 hidden md:inline">
                  {{ authStore.user?.nickname || authStore.user?.username }}
                </span>
                <Icon name="heroicons:chevron-down" class="w-4 h-4 ml-1" />
              </UButton>
            </UDropdown>

            <!-- 登录/注册 -->
            <div v-else class="flex items-center space-x-2">
              <UButton variant="ghost" size="sm" @click="navigateTo('/login')"> 登录 </UButton>
              <UButton size="sm" @click="navigateTo('/register')"> 注册 </UButton>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <!-- 主要内容区域 -->
    <main>
      <slot />
    </main>

    <!-- 底部 -->
    <footer class="bg-gray-900 text-white py-12 mt-16">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 class="text-lg font-semibold mb-4">关于我们</h3>
            <p class="text-gray-400 text-sm">社交购物网站致力于为用户提供优质的购物体验和社交互动平台。</p>
          </div>
          <div>
            <h3 class="text-lg font-semibold mb-4">购物指南</h3>
            <ul class="space-y-2 text-sm text-gray-400">
              <li><NuxtLink to="/help/shopping" class="hover:text-white">购物流程</NuxtLink></li>
              <li><NuxtLink to="/help/payment" class="hover:text-white">支付方式</NuxtLink></li>
              <li><NuxtLink to="/help/shipping" class="hover:text-white">配送说明</NuxtLink></li>
              <li><NuxtLink to="/help/return" class="hover:text-white">退换货政策</NuxtLink></li>
            </ul>
          </div>
          <div>
            <h3 class="text-lg font-semibold mb-4">客户服务</h3>
            <ul class="space-y-2 text-sm text-gray-400">
              <li><NuxtLink to="/contact" class="hover:text-white">联系我们</NuxtLink></li>
              <li><NuxtLink to="/help" class="hover:text-white">帮助中心</NuxtLink></li>
              <li><NuxtLink to="/feedback" class="hover:text-white">意见反馈</NuxtLink></li>
              <li><NuxtLink to="/service" class="hover:text-white">在线客服</NuxtLink></li>
            </ul>
          </div>
          <div>
            <h3 class="text-lg font-semibold mb-4">关注我们</h3>
            <div class="flex space-x-4">
              <a href="#" class="text-gray-400 hover:text-white">
                <Icon name="simple-icons:wechat" class="w-6 h-6" />
              </a>
              <a href="#" class="text-gray-400 hover:text-white">
                <Icon name="simple-icons:weibo" class="w-6 h-6" />
              </a>
              <a href="#" class="text-gray-400 hover:text-white">
                <Icon name="simple-icons:qq" class="w-6 h-6" />
              </a>
            </div>
          </div>
        </div>
        <div class="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
          <p>&copy; 2024 社交购物网站. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// 状态管理
const authStore = useAuthStore()
const cartStore = useCartStore()
const notificationsStore = useNotificationsStore()

// 响应式数据
const searchQuery = ref('')

// 用户菜单项
const userMenuItems = [
  [
    {
      label: '个人中心',
      icon: 'heroicons:user',
      click: () => navigateTo('/profile')
    }
  ],
  [
    {
      label: '我的订单',
      icon: 'heroicons:shopping-bag',
      click: () => navigateTo('/orders')
    },
    {
      label: '我的收藏',
      icon: 'heroicons:heart',
      click: () => navigateTo('/favorites')
    },
    {
      label: '我的动态',
      icon: 'heroicons:document-text',
      click: () => navigateTo('/social/posts')
    }
  ],
  [
    {
      label: '设置',
      icon: 'heroicons:cog-6-tooth',
      click: () => navigateTo('/settings')
    },
    {
      label: '退出登录',
      icon: 'heroicons:arrow-right-on-rectangle',
      click: () => handleLogout
    }
  ]
]

// 方法
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    navigateTo(`/search?q=${encodeURIComponent(searchQuery.value)}`)
  }
}

const handleLogout = async () => {
  await authStore.logout()
}

// 生命周期
onMounted(async () => {
  // 获取购物车数量和未读消息数量
  if (authStore.isLoggedIn) {
    try {
      // 并行获取购物车和未读通知数量
      await Promise.all([cartStore.fetchCart(), notificationsStore.fetchUnreadCount()])
    } catch (error) {
      console.error('初始化数据获取失败:', error)
    }
  }
})
</script>

<style scoped>
.container {
  max-width: 1200px;
}
</style>
