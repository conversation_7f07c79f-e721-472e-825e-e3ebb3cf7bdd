/**
 * 获取用户可用优惠券
 * GET /api/users/coupons
 */

import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  const query = getQuery(event)

  // 获取查询参数
  const status = (query.status as string) || 'UNUSED' // UNUSED | USED | EXPIRED
  const orderAmount = query.orderAmount ? parseFloat(query.orderAmount as string) : 0
  const productIds = query.productIds ? (query.productIds as string).split(',').map(id => parseInt(id)) : []
  const categoryIds = query.categoryIds ? (query.categoryIds as string).split(',').map(id => parseInt(id)) : []
  const merchantIds = query.merchantIds ? (query.merchantIds as string).split(',').map(id => parseInt(id)) : []

  try {
    const now = new Date()

    // 构建查询条件
    const where: any = {
      userId,
      status
    }

    // 如果查询未使用的优惠券，需要检查过期时间
    if (status === 'UNUSED') {
      where.expiresAt = { gte: now }
    }

    // 查询用户优惠券
    const userCoupons = await prisma.userCoupon.findMany({
      where,
      include: {
        coupon: true
      },
      orderBy: [
        { coupon: { type: 'asc' } }, // 同享券在前
        { expiresAt: 'asc' }, // 即将过期的在前
        { coupon: { discountValue: 'desc' } } // 优惠金额大的在前
      ]
    })

    // 过滤可用的优惠券
    const availableCoupons = userCoupons.filter(userCoupon => {
      const coupon = userCoupon.coupon

      // 检查优惠券状态
      if (coupon.status !== 'ACTIVE') return false

      // 检查时间有效性
      if (now < coupon.startTime || now > coupon.endTime) return false

      // 检查最低订单金额
      if (orderAmount > 0 && orderAmount < (coupon.minOrderAmount?.toNumber() || 0)) return false

      // 检查适用范围
      if (coupon.applicableType === 'ALL') return true

      if (coupon.applicableType === 'PRODUCT' && productIds.length > 0) {
        return productIds.some(id => coupon.applicableIds.includes(id))
      }

      if (coupon.applicableType === 'CATEGORY' && categoryIds.length > 0) {
        return categoryIds.some(id => coupon.applicableIds.includes(id))
      }

      if (coupon.applicableType === 'MERCHANT' && merchantIds.length > 0) {
        return merchantIds.some(id => coupon.applicableIds.includes(id))
      }

      return coupon.applicableType === 'ALL'
    })

    // 格式化优惠券数据
    const formattedCoupons = availableCoupons.map(userCoupon => {
      const coupon = userCoupon.coupon

      // 计算优惠金额
      let discountAmount = 0
      if (orderAmount > 0) {
        if (coupon.discountType === 'FIXED') {
          discountAmount = coupon.discountValue.toNumber()
        } else if (coupon.discountType === 'PERCENTAGE') {
          discountAmount = orderAmount * (coupon.discountValue.toNumber() / 100)
          if (coupon.maxDiscountAmount) {
            discountAmount = Math.min(discountAmount, coupon.maxDiscountAmount.toNumber())
          }
        }
      }

      return {
        id: userCoupon.id,
        couponId: coupon.id,
        name: coupon.name,
        description: coupon.description,
        type: coupon.type,
        discountType: coupon.discountType,
        discountValue: coupon.discountValue.toNumber(),
        minOrderAmount: coupon.minOrderAmount?.toNumber() || 0,
        maxDiscountAmount: coupon.maxDiscountAmount?.toNumber() || null,
        applicableType: coupon.applicableType,
        status: userCoupon.status,
        receivedAt: userCoupon.receivedAt,
        expiresAt: userCoupon.expiresAt,
        discountAmount, // 基于当前订单金额计算的优惠金额
        canUse: discountAmount > 0 && orderAmount >= (coupon.minOrderAmount?.toNumber() || 0)
      }
    })

    // 按类型分组
    const sharedCoupons = formattedCoupons.filter(c => c.type === 'SHARED')
    const exclusiveCoupons = formattedCoupons.filter(c => c.type === 'EXCLUSIVE')

    const result = {
      coupons: {
        shared: sharedCoupons,
        exclusive: exclusiveCoupons,
        total: formattedCoupons.length
      },
      summary: {
        totalCount: formattedCoupons.length,
        sharedCount: sharedCoupons.length,
        exclusiveCount: exclusiveCoupons.length,
        maxDiscount: Math.max(...formattedCoupons.map(c => c.discountAmount), 0)
      }
    }

    return createSuccessResponse(result, '获取用户优惠券成功')
  } catch (error) {
    console.error('获取用户优惠券失败:', error)
    throw new InternalServerError('获取用户优惠券失败')
  }
})
