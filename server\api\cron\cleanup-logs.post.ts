/**
 * 定时清理过期日志
 * POST /api/cron/cleanup-logs
 *
 * 这个接口应该通过定时任务调用，比如每天凌晨执行
 */

import { cleanupOldLogs } from '~/server/utils/logger'
import { defineApiHandler } from '~/utils/api'

export default defineApiHandler(async event => {
  // 检查是否是内部调用或管理员权限
  const authHeader = getHeader(event, 'authorization')
  const cronSecret = process.env.CRON_SECRET || 'default-cron-secret'

  // 验证定时任务密钥或管理员权限
  const isValidCronCall = authHeader === `Bearer ${cronSecret}`
  const isAdmin = event.context.user?.role === 'admin'

  if (!isValidCronCall && !isAdmin) {
    throw new AuthorizationError('无权限执行此操作')
  }

  try {
    // 默认保留90天的日志
    const daysToKeep = parseInt(process.env.LOG_RETENTION_DAYS || '90')

    console.log(`开始清理 ${daysToKeep} 天前的日志...`)

    const result = await cleanupOldLogs(daysToKeep)

    // 记录清理结果
    console.log(`日志清理完成:`, result)

    // 如果是定时任务调用，还可以发送通知或记录到监控系统
    if (isValidCronCall) {
      // 这里可以添加通知逻辑，比如发送邮件或推送到监控系统
      await notifyLogCleanup(result)
    }

    return createSuccessResponse(result, '日志清理完成')
  } catch (error) {
    console.error('定时清理日志失败:', error)

    // 如果是定时任务调用，发送错误通知
    if (isValidCronCall) {
      await notifyLogCleanupError(error)
    }

    throw new InternalServerError('清理日志失败')
  }
})

/**
 * 发送日志清理完成通知
 */
async function notifyLogCleanup(result: any) {
  try {
    // 这里可以实现具体的通知逻辑
    // 比如发送邮件、推送到Slack、记录到监控系统等

    console.log('日志清理通知:', {
      timestamp: new Date().toISOString(),
      searchLogsDeleted: result.searchLogsDeleted,
      userLogsDeleted: result.userLogsDeleted,
      totalDeleted: result.searchLogsDeleted + result.userLogsDeleted
    })

    // 示例：如果配置了邮件服务，可以发送通知邮件
    // await sendEmail({
    //   to: process.env.ADMIN_EMAIL,
    //   subject: '日志清理完成通知',
    //   text: `日志清理已完成，共删除 ${result.searchLogsDeleted + result.userLogsDeleted} 条记录`
    // })
  } catch (error) {
    console.error('发送日志清理通知失败:', error)
  }
}

/**
 * 发送日志清理错误通知
 */
async function notifyLogCleanupError(error: any) {
  try {
    console.error('日志清理错误通知:', {
      timestamp: new Date().toISOString(),
      error: error.message,
      stack: error.stack
    })

    // 示例：发送错误通知邮件
    // await sendEmail({
    //   to: process.env.ADMIN_EMAIL,
    //   subject: '日志清理失败通知',
    //   text: `日志清理失败: ${error.message}`
    // })
  } catch (notifyError) {
    console.error('发送错误通知失败:', notifyError)
  }
}
